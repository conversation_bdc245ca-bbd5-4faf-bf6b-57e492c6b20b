/**
 * @file gif_animatedgif_adapter.cpp
 * @brief AnimatedGIF框架适配层实现
 * 
 * 这个适配层将开源的AnimatedGIF框架适配到我们的gif_decoder接口
 */

#include "gif_animatedgif_adapter.h"
#include "../../src/AnimatedGIF.h"
#include <esp_log.h>
#include <esp_heap_caps.h>
#include <string.h>
#include <stdlib.h>

static const char* TAG = "GifAnimatedGifAdapter";

/**
 * @brief AnimatedGIF上下文结构
 */
struct gif_animatedgif_context {
    AnimatedGIF gif;                    // AnimatedGIF实例
    uint16_t* current_frame_buffer;     // 当前帧缓冲区
    uint32_t frame_width;               // 帧宽度
    uint32_t frame_height;              // 帧高度
    uint32_t current_delay_ms;          // 当前帧延迟
    bool frame_ready;                   // 帧是否准备好
};

// GIF绘制回调函数 - 将AnimatedGIF的输出转换为RGB565
static void gif_draw_callback(GIFDRAW *pDraw) {
    gif_animatedgif_context_t* ctx = (gif_animatedgif_context_t*)pDraw->pUser;
    if (!ctx || !ctx->current_frame_buffer) {
        return;
    }
    
    // 计算当前行在帧缓冲区中的位置
    uint16_t* line_buffer = &ctx->current_frame_buffer[pDraw->y * ctx->frame_width];
    
    // 将像素数据转换为RGB565格式
    if (pDraw->pPalette && pDraw->iWidth <= (int)ctx->frame_width) {
        for (int x = 0; x < pDraw->iWidth; x++) {
            uint8_t pixel_index = pDraw->pPixels[x];
            
            // 处理透明像素
            if (pDraw->ucHasTransparency && pixel_index == pDraw->ucTransparent) {
                // 透明像素保持原值或设为背景色
                continue;
            }
            
            // 从调色板获取RGB565颜色
            if (pixel_index < 256) {
                line_buffer[x] = pDraw->pPalette[pixel_index];
            }
        }
    }
}

extern "C" {

gif_animatedgif_adapter_t* gif_animatedgif_adapter_create(const lv_img_dsc_t* gif_source) {
    if (!gif_source || !gif_source->data || gif_source->data_size == 0) {
        ESP_LOGE(TAG, "Invalid GIF source");
        return nullptr;
    }
    
    // 分配适配器结构
    gif_animatedgif_adapter_t* adapter = (gif_animatedgif_adapter_t*)malloc(sizeof(gif_animatedgif_adapter_t));
    if (!adapter) {
        ESP_LOGE(TAG, "Failed to allocate adapter");
        return nullptr;
    }
    
    memset(adapter, 0, sizeof(gif_animatedgif_adapter_t));
    
    // 分配AnimatedGIF上下文
    adapter->context = (gif_animatedgif_context_t*)malloc(sizeof(gif_animatedgif_context_t));
    if (!adapter->context) {
        ESP_LOGE(TAG, "Failed to allocate context");
        free(adapter);
        return nullptr;
    }
    
    memset(adapter->context, 0, sizeof(gif_animatedgif_context_t));
    
    // 设置GIF源数据
    adapter->gif_source = gif_source;
    
    // 初始化AnimatedGIF
    int result = adapter->context->gif.open((uint8_t*)gif_source->data, 
                                           gif_source->data_size, 
                                           gif_draw_callback);
    
    if (result != 1) { // AnimatedGIF返回1表示成功
        ESP_LOGE(TAG, "Failed to open GIF with AnimatedGIF: %d", result);
        free(adapter->context);
        free(adapter);
        return nullptr;
    }
    
    // 获取GIF信息
    adapter->width = adapter->context->gif.getCanvasWidth();
    adapter->height = adapter->context->gif.getCanvasHeight();
    adapter->context->frame_width = adapter->width;
    adapter->context->frame_height = adapter->height;
    
    // 估算帧数 (AnimatedGIF可能需要完整解码才能获取准确帧数)
    adapter->frame_count = 50; // 默认值，后续可以通过解码过程更新
    
    // 分配帧缓冲区
    adapter->frame_buffer_size = adapter->width * adapter->height * sizeof(uint16_t);
    adapter->frame_buffer = (uint16_t*)heap_caps_malloc(adapter->frame_buffer_size, MALLOC_CAP_INTERNAL);
    if (!adapter->frame_buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        adapter->context->gif.close();
        free(adapter->context);
        free(adapter);
        return nullptr;
    }
    
    // 设置上下文的帧缓冲区指针
    adapter->context->current_frame_buffer = adapter->frame_buffer;
    
    adapter->current_frame = 0;
    adapter->is_initialized = true;
    
    ESP_LOGI(TAG, "AnimatedGIF adapter created: %lux%lu, estimated %lu frames",
             adapter->width, adapter->height, adapter->frame_count);
    
    return adapter;
}

void gif_animatedgif_adapter_destroy(gif_animatedgif_adapter_t* adapter) {
    if (!adapter) {
        return;
    }
    
    if (adapter->context) {
        adapter->context->gif.close();
        free(adapter->context);
    }
    
    if (adapter->frame_buffer) {
        free(adapter->frame_buffer);
    }
    
    free(adapter);
    ESP_LOGI(TAG, "AnimatedGIF adapter destroyed");
}

esp_err_t gif_animatedgif_adapter_get_info(gif_animatedgif_adapter_t* adapter, 
                                           uint32_t* width, uint32_t* height, 
                                           uint32_t* frame_count) {
    if (!adapter || !adapter->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (width) *width = adapter->width;
    if (height) *height = adapter->height;
    if (frame_count) *frame_count = adapter->frame_count;
    
    return ESP_OK;
}

esp_err_t gif_animatedgif_adapter_decode_frame(gif_animatedgif_adapter_t* adapter, 
                                              uint32_t frame_index,
                                              uint16_t* output_buffer, 
                                              uint32_t buffer_size,
                                              uint32_t* delay_ms) {
    if (!adapter || !adapter->is_initialized || !output_buffer || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint32_t required_size = adapter->width * adapter->height * sizeof(uint16_t);
    if (buffer_size < required_size) {
        return ESP_ERR_INVALID_SIZE;
    }
    
    // 如果需要解码不同的帧，重置到开始位置
    if (frame_index < adapter->current_frame) {
        adapter->context->gif.reset();
        adapter->current_frame = 0;
    }
    
    // 清空帧缓冲区
    memset(adapter->frame_buffer, 0, adapter->frame_buffer_size);
    adapter->context->frame_ready = false;
    
    // 解码到目标帧
    while (adapter->current_frame <= frame_index) {
        int delay_ms_int = 0;
        int result = adapter->context->gif.playFrame(true, &delay_ms_int, adapter->context);
        
        if (result != 1) { // AnimatedGIF返回1表示成功
            if (adapter->current_frame > 0) {
                // 可能到达文件末尾，重置到开始
                adapter->context->gif.reset();
                adapter->current_frame = 0;
                continue;
            } else {
                ESP_LOGE(TAG, "Failed to decode frame %lu, error: %d", frame_index, result);
                return ESP_FAIL;
            }
        }
        
        adapter->last_delay_ms = (delay_ms_int > 0) ? delay_ms_int : 100;
        
        if (adapter->current_frame == frame_index) {
            // 复制解码的帧数据到输出缓冲区
            memcpy(output_buffer, adapter->frame_buffer, required_size);
            *delay_ms = adapter->last_delay_ms;
            
            ESP_LOGD(TAG, "Decoded frame %lu: %lux%lu, delay=%lu ms",
                     frame_index, adapter->width, adapter->height, *delay_ms);
            
            adapter->current_frame++;
            return ESP_OK;
        }
        
        adapter->current_frame++;
    }
    
    return ESP_FAIL;
}

esp_err_t gif_animatedgif_adapter_reset(gif_animatedgif_adapter_t* adapter) {
    if (!adapter || !adapter->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }
    
    adapter->context->gif.reset();
    adapter->current_frame = 0;
    
    ESP_LOGD(TAG, "AnimatedGIF adapter reset");
    return ESP_OK;
}

} // extern "C"
