// ESP32适配的AnimatedGIF头文件
#pragma once

// ESP32平台定义
#define PICO_BUILD 1  // 使用类似Pico的配置
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <esp_heap_caps.h>

#define memcpy_P memcpy
#define PROGMEM

// ESP32S3支持非对齐访问
#define ALLOWS_UNALIGNED

// 为ESP32优化的配置
#define MAX_CODE_SIZE 12
#define MAX_COLORS 256
#define MAX_WIDTH 480
#define LZW_BUF_SIZE (6*255)
#define LZW_HIGHWATER (4*255)
#define FILE_BUF_SIZE (1<<MAX_CODE_SIZE)
#define PIXEL_FIRST 0
#define PIXEL_LAST (1<<MAX_CODE_SIZE)
#define LINK_UNUSED 5911
#define LINK_END 5912
#define MAX_HASH 5003

// Turbo模式配置
#define TURBO_BUFFER_SIZE 0x6100
#define LZW_BUF_SIZE_TURBO (LZW_BUF_SIZE + (2<<MAX_CODE_SIZE) + (PIXEL_LAST*2) + MAX_WIDTH)
#define LZW_HIGHWATER_TURBO ((LZW_BUF_SIZE_TURBO * 14) / 16)

// 像素类型
enum {
   GIF_PALETTE_RGB565_LE = 0,
   GIF_PALETTE_RGB565_BE,
   GIF_PALETTE_RGB888,
   GIF_PALETTE_RGB8888,
   GIF_PALETTE_1BPP,
   GIF_PALETTE_1BPP_OLED
};

// 绘制类型
enum {
   GIF_DRAW_RAW = 0,
   GIF_DRAW_COOKED
};

// 错误码
enum {
   GIF_SUCCESS = 0,
   GIF_DECODE_ERROR,
   GIF_TOO_WIDE,
   GIF_INVALID_PARAMETER,
   GIF_UNSUPPORTED_FEATURE,
   GIF_FILE_NOT_OPEN,
   GIF_EARLY_EOF,
   GIF_EMPTY_FRAME,
   GIF_BAD_FILE,
   GIF_ERROR_MEMORY
};

// 结构体定义
typedef struct gif_file_tag {
    int32_t iPos;
    int32_t iSize;
    uint8_t *pData;
    void * fHandle;
} GIFFILE;

typedef struct gif_info_tag {
    int32_t iFrameCount;
    int32_t iDuration;
    int32_t iMaxDelay;
    int32_t iMinDelay;
} GIFINFO;

typedef struct gif_draw_tag {
    int iX, iY;
    int y;
    int iWidth, iHeight;
    int iCanvasWidth;
    void *pUser;
    uint8_t *pPixels;
    uint16_t *pPalette;
    uint8_t *pPalette24;
    uint8_t ucTransparent;
    uint8_t ucHasTransparency;
    uint8_t ucDisposalMethod;
    uint8_t ucBackground;
    uint8_t ucPaletteType;
    uint8_t ucIsGlobalPalette;
} GIFDRAW;

// 回调函数类型
typedef int32_t (GIF_READ_CALLBACK)(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen);
typedef int32_t (GIF_SEEK_CALLBACK)(GIFFILE *pFile, int32_t iPosition);
typedef void (GIF_DRAW_CALLBACK)(GIFDRAW *pDraw);
typedef void * (GIF_OPEN_CALLBACK)(const char *szFilename, int32_t *pFileSize);
typedef void (GIF_CLOSE_CALLBACK)(void *pHandle);
typedef void * (GIF_ALLOC_CALLBACK)(uint32_t iSize);
typedef void (GIF_FREE_CALLBACK)(void *buffer);

// GIF图像结构
typedef struct gif_image_tag {
    uint16_t iWidth, iHeight, iCanvasWidth, iCanvasHeight;
    uint16_t iX, iY;
    uint16_t iBpp;
    int16_t iError;
    uint16_t iFrameDelay;
    int16_t iRepeatCount;
    uint16_t iXCount, iYCount;
    int iLZWOff;
    int iLZWSize;
    int iCommentPos;
    short sCommentLen;
    unsigned char bEndOfFrame;
    unsigned char ucGIFBits, ucBackground, ucTransparent, ucCodeStart, ucMap, bUseLocalPalette;
    unsigned char ucPaletteType;
    unsigned char ucDrawType;
    GIF_READ_CALLBACK *pfnRead;
    GIF_SEEK_CALLBACK *pfnSeek;
    GIF_DRAW_CALLBACK *pfnDraw;
    GIF_OPEN_CALLBACK *pfnOpen;
    GIF_CLOSE_CALLBACK *pfnClose;
    GIFFILE GIFFile;
    void *pUser;
    unsigned char *pFrameBuffer;
    unsigned char *pTurboBuffer;
    unsigned char *pPixels, *pOldPixels;
    unsigned char ucFileBuf[FILE_BUF_SIZE];
    unsigned short pPalette[(MAX_COLORS * 3)/2];
    unsigned short pLocalPalette[(MAX_COLORS * 3)/2];
    unsigned char ucLZW[LZW_BUF_SIZE];
    unsigned short usGIFTable[1<<MAX_CODE_SIZE];
    unsigned char ucGIFPixels[(PIXEL_LAST*2)];
    unsigned char ucLineBuf[MAX_WIDTH];
} GIFIMAGE;

// C接口函数声明
#ifdef __cplusplus
extern "C" {
#endif

int GIF_openRAM(GIFIMAGE *pGIF, uint8_t *pData, int iDataSize, GIF_DRAW_CALLBACK *pfnDraw);
void GIF_close(GIFIMAGE *pGIF);
void GIF_begin(GIFIMAGE *pGIF, unsigned char ucPaletteType);
void GIF_reset(GIFIMAGE *pGIF);
int GIF_playFrame(GIFIMAGE *pGIF, int *delayMilliseconds, void *pUser);
int GIF_getCanvasWidth(GIFIMAGE *pGIF);
int GIF_getCanvasHeight(GIFIMAGE *pGIF);
int GIF_getInfo(GIFIMAGE *pGIF, GIFINFO *pInfo);
int GIF_getLastError(GIFIMAGE *pGIF);

// 内存读取回调函数
int32_t readMem(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen);
int32_t seekMem(GIFFILE *pFile, int32_t iPosition);

// 内部初始化函数
int GIFInit(GIFIMAGE *pGIF);

#ifdef __cplusplus
}
#endif

// 平台特定的宏定义
#if (INTPTR_MAX == INT64_MAX)
#define ALLOWS_UNALIGNED
#define INTELSHORT(p) (*(uint16_t *)p)
#define INTELLONG(p) (*(uint64_t *)p)
#define REGISTER_WIDTH 64
#define BIGINT int64_t
#define BIGUINT uint64_t
#else
#define REGISTER_WIDTH 32
#ifdef ALLOWS_UNALIGNED
#define INTELSHORT(p) (*(uint16_t *)p)
#define INTELLONG(p) (*(uint32_t *)p)
#else
#define INTELSHORT(p) ((*p) + (*(p+1)<<8))
#define INTELLONG(p) ((*p) + (*(p+1)<<8) + (*(p+2)<<16) + (*(p+3)<<24))
#endif
#define BIGINT int32_t
#define BIGUINT uint32_t
#endif
