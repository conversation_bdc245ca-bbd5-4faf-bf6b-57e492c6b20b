#pragma once

#include <lvgl.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/semphr.h>
#include <esp_heap_caps.h>
#include <esp_log.h>
#include "gif_config.h"

#if GIF_DEBUG_PERFORMANCE
#include "gif_performance_monitor.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

// GIF帧缓存管理器
// 负责在PSRAM中预缓存解码后的GIF帧数据，提升播放性能

/**
 * @brief GIF帧数据结构
 */
typedef struct {
    uint16_t* frame_data;           // 解码后的RGB565帧数据 (PSRAM)
    uint32_t frame_size;            // 帧数据大小 (字节)
    uint32_t frame_index;           // 帧索引
    uint32_t delay_ms;              // 帧延迟时间 (毫秒)
    bool is_valid;                  // 帧数据是否有效
} gif_frame_t;

/**
 * @brief GIF缓存统计信息
 */
typedef struct {
    uint32_t total_frames;          // GIF总帧数
    uint32_t cached_frames;         // 已缓存帧数
    uint32_t cache_hits;            // 缓存命中次数
    uint32_t cache_misses;          // 缓存未命中次数
    uint32_t decode_time_total_ms;  // 总解码时间
    uint32_t decode_count;          // 解码次数
    float avg_decode_time_ms;       // 平均解码时间
    float cache_hit_rate;           // 缓存命中率
} gif_cache_stats_t;

/**
 * @brief GIF帧缓存管理器结构
 */
typedef struct {
    // 基本信息
    const lv_img_dsc_t* gif_source; // GIF源数据
    uint32_t width;                 // 帧宽度
    uint32_t height;                // 帧高度
    uint32_t total_frames;          // 总帧数

    // 解码器
    void* gif_decoder;              // GIF解码器 (gif_decoder_t*)

    // 缓存数据
    gif_frame_t* frame_cache;       // 帧缓存数组 (PSRAM)
    uint32_t cache_size;            // 缓存大小 (帧数)
    uint32_t current_frame;         // 当前播放帧索引
    uint32_t cache_start_frame;     // 缓存起始帧索引

    // 解码控制
    TaskHandle_t decode_task_handle; // 解码任务句柄
    QueueHandle_t decode_queue;     // 解码请求队列
    SemaphoreHandle_t cache_mutex;  // 缓存访问互斥锁
    bool decode_running;            // 解码任务是否运行
    bool preload_complete;          // 预加载是否完成

    // 统计信息
    gif_cache_stats_t stats;        // 缓存统计

    // 内存管理
    size_t total_cache_memory;      // 总缓存内存大小
    bool use_psram;                 // 是否使用PSRAM

#if GIF_DEBUG_PERFORMANCE
    // 性能监控
    void* performance_monitor;      // 性能监控器 (gif_performance_monitor_t*)
#endif
} gif_frame_cache_t;

/**
 * @brief 解码请求消息
 */
typedef struct {
    uint32_t frame_index;           // 要解码的帧索引
    bool is_priority;               // 是否优先解码
} decode_request_t;

/**
 * @brief 创建GIF帧缓存管理器
 * 
 * @param gif_source GIF源数据
 * @param width 帧宽度
 * @param height 帧高度
 * @param cache_size 缓存大小 (帧数)
 * @param use_psram 是否使用PSRAM
 * @return gif_frame_cache_t* 缓存管理器指针，失败返回NULL
 */
gif_frame_cache_t* gif_frame_cache_create(const lv_img_dsc_t* gif_source,
                                          uint32_t width, uint32_t height,
                                          uint32_t cache_size, bool use_psram);

/**
 * @brief 销毁GIF帧缓存管理器
 * 
 * @param cache 缓存管理器指针
 */
void gif_frame_cache_destroy(gif_frame_cache_t* cache);

/**
 * @brief 启动预解码任务
 * 
 * @param cache 缓存管理器指针
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_frame_cache_start_decode_task(gif_frame_cache_t* cache);

/**
 * @brief 停止预解码任务
 * 
 * @param cache 缓存管理器指针
 */
void gif_frame_cache_stop_decode_task(gif_frame_cache_t* cache);

/**
 * @brief 获取指定帧的数据
 * 
 * @param cache 缓存管理器指针
 * @param frame_index 帧索引
 * @param frame_data 输出帧数据指针
 * @param delay_ms 输出帧延迟时间
 * @return esp_err_t ESP_OK成功，ESP_ERR_NOT_FOUND未找到
 */
esp_err_t gif_frame_cache_get_frame(gif_frame_cache_t* cache, uint32_t frame_index,
                                   uint16_t** frame_data, uint32_t* delay_ms);

/**
 * @brief 预加载下一批帧
 * 
 * @param cache 缓存管理器指针
 * @param start_frame 起始帧索引
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_frame_cache_preload_frames(gif_frame_cache_t* cache, uint32_t start_frame);

/**
 * @brief 获取缓存统计信息
 * 
 * @param cache 缓存管理器指针
 * @return gif_cache_stats_t* 统计信息指针
 */
const gif_cache_stats_t* gif_frame_cache_get_stats(gif_frame_cache_t* cache);

/**
 * @brief 打印缓存统计信息
 * 
 * @param cache 缓存管理器指针
 */
void gif_frame_cache_print_stats(gif_frame_cache_t* cache);

/**
 * @brief 重置缓存统计信息
 * 
 * @param cache 缓存管理器指针
 */
void gif_frame_cache_reset_stats(gif_frame_cache_t* cache);

#ifdef __cplusplus
}
#endif
