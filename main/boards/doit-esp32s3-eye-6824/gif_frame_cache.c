#include "gif_frame_cache.h"
#include "gif_decoder.h"
#include <string.h>
#include <sys/time.h>

static const char* TAG = "GifFrameCache";

// 解码任务队列大小
#define DECODE_QUEUE_SIZE 20

// 获取当前时间戳 (毫秒)
static uint32_t get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (tv.tv_sec * 1000) + (tv.tv_usec / 1000);
}

// 解码单帧GIF数据
static esp_err_t decode_gif_frame(gif_decoder_t* decoder, uint32_t frame_index,
                                 uint32_t width, uint32_t height, uint16_t* output_buffer,
                                 uint32_t* delay_ms) {
    if (!decoder || !output_buffer || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    uint32_t start_time = get_timestamp_ms();

    // 使用GIF解码器解码指定帧
    uint32_t buffer_size = width * height * sizeof(uint16_t);
    esp_err_t ret = gif_decoder_decode_frame(decoder, frame_index, output_buffer,
                                           buffer_size, delay_ms);

    uint32_t decode_time = get_timestamp_ms() - start_time;
    ESP_LOGD(TAG, "Decoded frame %lu in %lu ms", frame_index, decode_time);

    return ret;
}

// 解码任务函数
static void decode_task(void* parameter) {
    gif_frame_cache_t* cache = (gif_frame_cache_t*)parameter;
    decode_request_t request;
    
    ESP_LOGI(TAG, "Decode task started on core %d", xPortGetCoreID());
    
    while (cache->decode_running) {
        // 等待解码请求
        if (xQueueReceive(cache->decode_queue, &request, pdMS_TO_TICKS(1000)) == pdTRUE) {
            uint32_t frame_index = request.frame_index;
            
            // 检查帧索引有效性
            if (frame_index >= cache->total_frames) {
                ESP_LOGW(TAG, "Invalid frame index: %lu", frame_index);
                continue;
            }
            
            // 计算缓存位置
            uint32_t cache_index = frame_index % cache->cache_size;
            
            // 获取缓存锁
            if (xSemaphoreTake(cache->cache_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
                gif_frame_t* frame = &cache->frame_cache[cache_index];
                
                // 检查是否需要解码
                if (!frame->is_valid || frame->frame_index != frame_index) {
                    uint32_t start_time = get_timestamp_ms();

                    // 解码帧数据
                    esp_err_t ret = decode_gif_frame((gif_decoder_t*)cache->gif_decoder, frame_index,
                                                   cache->width, cache->height,
                                                   frame->frame_data, &frame->delay_ms);
                    
                    if (ret == ESP_OK) {
                        frame->frame_index = frame_index;
                        frame->is_valid = true;
                        
                        // 更新统计信息
                        uint32_t decode_time = get_timestamp_ms() - start_time;
                        cache->stats.decode_time_total_ms += decode_time;
                        cache->stats.decode_count++;
                        cache->stats.avg_decode_time_ms = 
                            (float)cache->stats.decode_time_total_ms / cache->stats.decode_count;
                        
                        ESP_LOGD(TAG, "Decoded frame %lu to cache[%lu] in %lu ms", 
                                frame_index, cache_index, decode_time);
                    } else {
                        ESP_LOGE(TAG, "Failed to decode frame %lu: %s", 
                                frame_index, esp_err_to_name(ret));
                    }
                }
                
                xSemaphoreGive(cache->cache_mutex);
            } else {
                ESP_LOGW(TAG, "Failed to acquire cache mutex for frame %lu", frame_index);
            }
        }
    }
    
    ESP_LOGI(TAG, "Decode task stopped");
    vTaskDelete(NULL);
}

gif_frame_cache_t* gif_frame_cache_create(const lv_img_dsc_t* gif_source,
                                          uint32_t width, uint32_t height,
                                          uint32_t cache_size, bool use_psram) {
    if (!gif_source || width == 0 || height == 0 || cache_size == 0) {
        ESP_LOGE(TAG, "Invalid parameters");
        return NULL;
    }
    
    // 分配缓存管理器结构
    gif_frame_cache_t* cache = (gif_frame_cache_t*)malloc(sizeof(gif_frame_cache_t));
    if (!cache) {
        ESP_LOGE(TAG, "Failed to allocate cache structure");
        return NULL;
    }
    
    memset(cache, 0, sizeof(gif_frame_cache_t));
    
    // 创建GIF解码器
    gif_decoder_t* decoder = gif_decoder_create(gif_source);
    if (!decoder) {
        ESP_LOGE(TAG, "Failed to create GIF decoder");
        free(cache);
        return NULL;
    }

    // 获取GIF信息
    uint32_t gif_width, gif_height, gif_frame_count;
    if (gif_decoder_get_info(decoder, &gif_width, &gif_height, &gif_frame_count) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get GIF info");
        gif_decoder_destroy(decoder);
        free(cache);
        return NULL;
    }

    // 初始化基本信息
    cache->gif_source = gif_source;
    cache->gif_decoder = decoder;
    cache->width = gif_width;
    cache->height = gif_height;
    cache->cache_size = cache_size;
    cache->use_psram = use_psram;
    cache->total_frames = gif_frame_count;
    
    // 计算内存需求
    uint32_t frame_size = cache->width * cache->height * sizeof(uint16_t); // RGB565
    cache->total_cache_memory = frame_size * cache_size;
    
    ESP_LOGI(TAG, "Creating frame cache: %lux%lu, %lu/%lu frames, %lu bytes %s",
             cache->width, cache->height, cache_size, cache->total_frames,
             cache->total_cache_memory, use_psram ? "(PSRAM)" : "(Internal)");
    
    // 分配帧缓存数组 - 结构体数组使用内部RAM，数据缓冲区使用PSRAM
    uint32_t data_malloc_caps = use_psram ? MALLOC_CAP_SPIRAM : MALLOC_CAP_INTERNAL;
    cache->frame_cache = (gif_frame_t*)heap_caps_malloc(
        cache_size * sizeof(gif_frame_t), MALLOC_CAP_INTERNAL);
    
    if (!cache->frame_cache) {
        ESP_LOGE(TAG, "Failed to allocate frame cache array");
        free(cache);
        return NULL;
    }
    
    // 初始化帧缓存
    for (uint32_t i = 0; i < cache_size; i++) {
        gif_frame_t* frame = &cache->frame_cache[i];
        
        // 分配帧数据缓冲区到PSRAM
        frame->frame_data = (uint16_t*)heap_caps_malloc(frame_size, data_malloc_caps);
        if (!frame->frame_data) {
            ESP_LOGE(TAG, "Failed to allocate frame data buffer %lu (%lu bytes) in %s",
                     i, frame_size, use_psram ? "PSRAM" : "Internal RAM");

            // 打印内存使用情况
            size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
            size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
            ESP_LOGE(TAG, "Memory status - Internal: %zu bytes, PSRAM: %zu bytes",
                     free_internal, free_psram);

            // 清理已分配的缓冲区
            for (uint32_t j = 0; j < i; j++) {
                free(cache->frame_cache[j].frame_data);
            }
            free(cache->frame_cache);
            gif_decoder_destroy(decoder);
            free(cache);
            return NULL;
        }

        // 验证内存分配位置
        if (use_psram) {
            // 检查是否真的分配在PSRAM中
            if (!esp_ptr_external_ram(frame->frame_data)) {
                ESP_LOGW(TAG, "Frame buffer %lu allocated in internal RAM instead of PSRAM", i);
            }
        }
        
        frame->frame_size = frame_size;
        frame->frame_index = UINT32_MAX; // 无效索引
        frame->delay_ms = 0;
        frame->is_valid = false;
    }
    
    // 创建互斥锁
    cache->cache_mutex = xSemaphoreCreateMutex();
    if (!cache->cache_mutex) {
        ESP_LOGE(TAG, "Failed to create cache mutex");
        // 清理资源
        for (uint32_t i = 0; i < cache_size; i++) {
            free(cache->frame_cache[i].frame_data);
        }
        free(cache->frame_cache);
        free(cache);
        return NULL;
    }
    
    // 创建解码队列
    cache->decode_queue = xQueueCreate(DECODE_QUEUE_SIZE, sizeof(decode_request_t));
    if (!cache->decode_queue) {
        ESP_LOGE(TAG, "Failed to create decode queue");
        vSemaphoreDelete(cache->cache_mutex);
        for (uint32_t i = 0; i < cache_size; i++) {
            free(cache->frame_cache[i].frame_data);
        }
        free(cache->frame_cache);
        free(cache);
        return NULL;
    }
    
    // 初始化统计信息
    cache->stats.total_frames = cache->total_frames;
    cache->stats.cached_frames = 0;
    
    ESP_LOGI(TAG, "Frame cache created successfully");
    return cache;
}

void gif_frame_cache_destroy(gif_frame_cache_t* cache) {
    if (!cache) {
        return;
    }

    ESP_LOGI(TAG, "Destroying frame cache");

    // 停止解码任务
    gif_frame_cache_stop_decode_task(cache);

    // 清理GIF解码器
    if (cache->gif_decoder) {
        gif_decoder_destroy((gif_decoder_t*)cache->gif_decoder);
    }

    // 清理队列
    if (cache->decode_queue) {
        vQueueDelete(cache->decode_queue);
    }

    // 清理互斥锁
    if (cache->cache_mutex) {
        vSemaphoreDelete(cache->cache_mutex);
    }

    // 清理帧缓存
    if (cache->frame_cache) {
        for (uint32_t i = 0; i < cache->cache_size; i++) {
            if (cache->frame_cache[i].frame_data) {
                free(cache->frame_cache[i].frame_data);
            }
        }
        free(cache->frame_cache);
    }

    // 清理缓存管理器
    free(cache);

    ESP_LOGI(TAG, "Frame cache destroyed");
}

esp_err_t gif_frame_cache_start_decode_task(gif_frame_cache_t* cache) {
    if (!cache) {
        return ESP_ERR_INVALID_ARG;
    }

    if (cache->decode_running) {
        ESP_LOGW(TAG, "Decode task already running");
        return ESP_OK;
    }

    cache->decode_running = true;

    BaseType_t result = xTaskCreatePinnedToCore(
        decode_task,
        "gif_decode_task",
        GIF_FRAME_DECODE_TASK_STACK,
        cache,
        GIF_FRAME_DECODE_TASK_PRIORITY,
        &cache->decode_task_handle,
        GIF_FRAME_DECODE_TASK_CORE
    );

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create decode task");
        cache->decode_running = false;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Decode task started on core %d", GIF_FRAME_DECODE_TASK_CORE);
    return ESP_OK;
}

void gif_frame_cache_stop_decode_task(gif_frame_cache_t* cache) {
    if (!cache || !cache->decode_running) {
        return;
    }

    ESP_LOGI(TAG, "Stopping decode task");
    cache->decode_running = false;

    // 等待任务结束
    if (cache->decode_task_handle) {
        // 发送停止信号并等待任务结束
        vTaskDelay(pdMS_TO_TICKS(100));
        cache->decode_task_handle = NULL;
    }
}

esp_err_t gif_frame_cache_get_frame(gif_frame_cache_t* cache, uint32_t frame_index,
                                   uint16_t** frame_data, uint32_t* delay_ms) {
    if (!cache || !frame_data || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    if (frame_index >= cache->total_frames) {
        return ESP_ERR_INVALID_ARG;
    }

    uint32_t cache_index = frame_index % cache->cache_size;

    // 获取缓存锁
    if (xSemaphoreTake(cache->cache_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGW(TAG, "Failed to acquire cache mutex");
        cache->stats.cache_misses++;
        return ESP_ERR_TIMEOUT;
    }

    gif_frame_t* frame = &cache->frame_cache[cache_index];

    // 检查缓存命中
    if (frame->is_valid && frame->frame_index == frame_index) {
        // 缓存命中
        *frame_data = frame->frame_data;
        *delay_ms = frame->delay_ms;
        cache->stats.cache_hits++;

        xSemaphoreGive(cache->cache_mutex);

        ESP_LOGD(TAG, "Cache hit for frame %lu", frame_index);
        return ESP_OK;
    } else {
        // 缓存未命中
        cache->stats.cache_misses++;
        xSemaphoreGive(cache->cache_mutex);

        // 请求解码该帧
        decode_request_t request = {
            .frame_index = frame_index,
            .is_priority = true
        };

        if (xQueueSend(cache->decode_queue, &request, pdMS_TO_TICKS(10)) != pdTRUE) {
            ESP_LOGW(TAG, "Failed to send decode request for frame %lu", frame_index);
        }

        ESP_LOGD(TAG, "Cache miss for frame %lu, requested decode", frame_index);
        return ESP_ERR_NOT_FOUND;
    }
}

esp_err_t gif_frame_cache_preload_frames(gif_frame_cache_t* cache, uint32_t start_frame) {
    if (!cache) {
        return ESP_ERR_INVALID_ARG;
    }

    if (start_frame >= cache->total_frames) {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Preloading frames starting from %lu", start_frame);

    // 计算要预加载的帧数量
    uint32_t frames_to_load = cache->cache_size;
    if (start_frame + frames_to_load > cache->total_frames) {
        frames_to_load = cache->total_frames - start_frame;
    }

    // 发送解码请求
    for (uint32_t i = 0; i < frames_to_load; i++) {
        uint32_t frame_index = (start_frame + i) % cache->total_frames;

        decode_request_t request = {
            .frame_index = frame_index,
            .is_priority = false
        };

        if (xQueueSend(cache->decode_queue, &request, pdMS_TO_TICKS(10)) != pdTRUE) {
            ESP_LOGW(TAG, "Failed to send preload request for frame %lu", frame_index);
        }
    }

    cache->cache_start_frame = start_frame;

    ESP_LOGI(TAG, "Preload requests sent for %lu frames", frames_to_load);
    return ESP_OK;
}

const gif_cache_stats_t* gif_frame_cache_get_stats(gif_frame_cache_t* cache) {
    if (!cache) {
        return NULL;
    }

    // 更新缓存命中率
    uint32_t total_requests = cache->stats.cache_hits + cache->stats.cache_misses;
    if (total_requests > 0) {
        cache->stats.cache_hit_rate = (float)cache->stats.cache_hits / total_requests * 100.0f;
    }

    // 统计已缓存帧数
    if (xSemaphoreTake(cache->cache_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        uint32_t cached_count = 0;
        for (uint32_t i = 0; i < cache->cache_size; i++) {
            if (cache->frame_cache[i].is_valid) {
                cached_count++;
            }
        }
        cache->stats.cached_frames = cached_count;
        xSemaphoreGive(cache->cache_mutex);
    }

    return &cache->stats;
}

void gif_frame_cache_print_stats(gif_frame_cache_t* cache) {
    if (!cache) {
        return;
    }

    const gif_cache_stats_t* stats = gif_frame_cache_get_stats(cache);

    ESP_LOGI(TAG, "=== GIF Frame Cache Statistics ===");
    ESP_LOGI(TAG, "Total frames: %lu", stats->total_frames);
    ESP_LOGI(TAG, "Cached frames: %lu/%lu", stats->cached_frames, cache->cache_size);
    ESP_LOGI(TAG, "Cache hits: %lu", stats->cache_hits);
    ESP_LOGI(TAG, "Cache misses: %lu", stats->cache_misses);
    ESP_LOGI(TAG, "Cache hit rate: %.1f%%", stats->cache_hit_rate);
    ESP_LOGI(TAG, "Decode count: %lu", stats->decode_count);
    ESP_LOGI(TAG, "Avg decode time: %.1f ms", stats->avg_decode_time_ms);
    ESP_LOGI(TAG, "Total decode time: %lu ms", stats->decode_time_total_ms);
    ESP_LOGI(TAG, "Cache memory: %lu bytes (%s)",
             cache->total_cache_memory, cache->use_psram ? "PSRAM" : "Internal");

    // 内存使用情况
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "Free memory - Internal: %zu bytes, PSRAM: %zu bytes",
             free_internal, free_psram);
}

void gif_frame_cache_reset_stats(gif_frame_cache_t* cache) {
    if (!cache) {
        return;
    }

    cache->stats.cache_hits = 0;
    cache->stats.cache_misses = 0;
    cache->stats.decode_time_total_ms = 0;
    cache->stats.decode_count = 0;
    cache->stats.avg_decode_time_ms = 0.0f;
    cache->stats.cache_hit_rate = 0.0f;

    ESP_LOGI(TAG, "Cache statistics reset");
}
