#include "gif_decoder.h"
#include "gif_animatedgif_adapter.h"
#include <esp_log.h>
#include <esp_heap_caps.h>
#include <string.h>
#include <stdlib.h>

static const char* TAG = "GifDecoder";

gif_decoder_t* gif_decoder_create(const lv_img_dsc_t* gif_source) {
    if (!gif_source || !gif_source->data || gif_source->data_size == 0) {
        ESP_LOGE(TAG, "Invalid GIF source");
        return NULL;
    }

    // 分配解码器结构
    gif_decoder_t* decoder = (gif_decoder_t*)malloc(sizeof(gif_decoder_t));
    if (!decoder) {
        ESP_LOGE(TAG, "Failed to allocate decoder");
        return NULL;
    }

    memset(decoder, 0, sizeof(gif_decoder_t));

    // 创建AnimatedGIF适配器
    decoder->adapter = gif_animatedgif_adapter_create(gif_source);
    if (!decoder->adapter) {
        ESP_LOGE(TAG, "Failed to create AnimatedGIF adapter");
        free(decoder);
        return NULL;
    }

    // 设置GIF源数据
    decoder->gif_source = gif_source;

    // 获取GIF信息
    esp_err_t ret = gif_animatedgif_adapter_get_info(decoder->adapter,
                                                    &decoder->width,
                                                    &decoder->height,
                                                    &decoder->frame_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get GIF info");
        gif_animatedgif_adapter_destroy(decoder->adapter);
        free(decoder);
        return NULL;
    }

    decoder->current_frame = 0;
    decoder->is_initialized = true;

    ESP_LOGI(TAG, "GIF decoder created: %lux%lu, %lu frames",
             decoder->width, decoder->height, decoder->frame_count);

    return decoder;
}

void gif_decoder_destroy(gif_decoder_t* decoder) {
    if (!decoder) {
        return;
    }

    if (decoder->adapter) {
        gif_animatedgif_adapter_destroy(decoder->adapter);
    }

    free(decoder);
    ESP_LOGI(TAG, "GIF decoder destroyed");
}

esp_err_t gif_decoder_get_info(gif_decoder_t* decoder, uint32_t* width,
                              uint32_t* height, uint32_t* frame_count) {
    if (!decoder || !decoder->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    if (width) *width = decoder->width;
    if (height) *height = decoder->height;
    if (frame_count) *frame_count = decoder->frame_count;

    return ESP_OK;
}

esp_err_t gif_decoder_decode_frame(gif_decoder_t* decoder, uint32_t frame_index,
                                  uint16_t* output_buffer, uint32_t buffer_size,
                                  uint32_t* delay_ms) {
    if (!decoder || !decoder->is_initialized || !output_buffer || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    // 使用适配器解码帧
    esp_err_t ret = gif_animatedgif_adapter_decode_frame(decoder->adapter,
                                                        frame_index,
                                                        output_buffer,
                                                        buffer_size,
                                                        delay_ms);

    if (ret == ESP_OK) {
        ESP_LOGD(TAG, "Decoded frame %lu: %lux%lu, delay=%lu ms",
                 frame_index, decoder->width, decoder->height, *delay_ms);
    }

    return ret;
}

esp_err_t gif_decoder_reset(gif_decoder_t* decoder) {
    if (!decoder || !decoder->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    esp_err_t ret = gif_animatedgif_adapter_reset(decoder->adapter);
    if (ret == ESP_OK) {
        decoder->current_frame = 0;
        ESP_LOGD(TAG, "GIF decoder reset");
    }

    return ret;
}
