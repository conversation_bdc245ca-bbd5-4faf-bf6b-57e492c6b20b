#include "gif_decoder.h"
#include <esp_log.h>
#include <esp_heap_caps.h>
#include <string.h>
#include <stdlib.h>

// 包含AnimatedGIF框架
extern "C" {
#include "../../src/AnimatedGIF.h"
}

static const char* TAG = "GifDecoder";

// 内存文件读取回调
static int32_t gif_read_callback(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen) {
    if (!pFile || !pFile->pData || !pBuf) {
        return 0;
    }

    // 检查读取范围
    if (pFile->iPos + iLen > pFile->iSize) {
        iLen = pFile->iSize - pFile->iPos;
    }

    if (iLen <= 0) {
        return 0;
    }

    // 从内存中复制数据
    memcpy(pBuf, &pFile->pData[pFile->iPos], iLen);
    pFile->iPos += iLen;

    return iLen;
}

// 内存文件定位回调
static int32_t gif_seek_callback(GIFFILE *pFile, int32_t iPosition) {
    if (!pFile || !pFile->pData) {
        return 0;
    }

    // 限制定位范围
    if (iPosition < 0) {
        iPosition = 0;
    } else if (iPosition > pFile->iSize) {
        iPosition = pFile->iSize;
    }

    pFile->iPos = iPosition;
    return iPosition;
}

// GIF绘制回调 - 用于捕获解码的帧数据
static void gif_draw_callback(GIFDRAW *pDraw) {
    gif_decoder_t* decoder = (gif_decoder_t*)pDraw->pUser;
    if (!decoder || !decoder->frame_buffer) {
        return;
    }

    // 计算当前行在帧缓冲区中的位置
    uint16_t* line_buffer = &decoder->frame_buffer[pDraw->y * decoder->width];

    // 如果是RGB565格式，直接复制
    if (pDraw->pPalette && pDraw->iWidth <= decoder->width) {
        for (int x = 0; x < pDraw->iWidth; x++) {
            uint8_t pixel = pDraw->pPixels[x];
            if (pDraw->ucHasTransparency && pixel == pDraw->ucTransparent) {
                // 透明像素保持原值或设为背景色
                continue;
            }
            line_buffer[x] = pDraw->pPalette[pixel];
        }
    }
}

gif_decoder_t* gif_decoder_create(const lv_img_dsc_t* gif_source) {
    if (!gif_source || !gif_source->data || gif_source->data_size == 0) {
        ESP_LOGE(TAG, "Invalid GIF source");
        return NULL;
    }

    // 分配解码器结构
    gif_decoder_t* decoder = (gif_decoder_t*)malloc(sizeof(gif_decoder_t));
    if (!decoder) {
        ESP_LOGE(TAG, "Failed to allocate decoder");
        return NULL;
    }

    memset(decoder, 0, sizeof(gif_decoder_t));

    // 分配AnimatedGIF实例
    decoder->gif_image = (GIFIMAGE*)malloc(sizeof(GIFIMAGE));
    if (!decoder->gif_image) {
        ESP_LOGE(TAG, "Failed to allocate GIF image");
        free(decoder);
        return NULL;
    }

    memset(decoder->gif_image, 0, sizeof(GIFIMAGE));

    // 设置GIF源数据
    decoder->gif_source = gif_source;

    // 初始化AnimatedGIF
    decoder->gif_image->pfnRead = gif_read_callback;
    decoder->gif_image->pfnSeek = gif_seek_callback;
    decoder->gif_image->pfnDraw = gif_draw_callback;
    decoder->gif_image->GIFFile.pData = (uint8_t*)gif_source->data;
    decoder->gif_image->GIFFile.iSize = gif_source->data_size;
    decoder->gif_image->GIFFile.iPos = 0;
    decoder->gif_image->pUser = decoder;

    // 初始化GIF解码器
    if (GIF_openRAM(decoder->gif_image, (uint8_t*)gif_source->data,
                    gif_source->data_size, gif_draw_callback) != GIF_SUCCESS) {
        ESP_LOGE(TAG, "Failed to open GIF");
        free(decoder->gif_image);
        free(decoder);
        return NULL;
    }

    // 开始解码以获取基本信息
    GIF_begin(decoder->gif_image, GIF_PALETTE_RGB565_LE);

    // 获取GIF信息
    decoder->width = decoder->gif_image->iCanvasWidth;
    decoder->height = decoder->gif_image->iCanvasHeight;

    // 获取帧数信息
    GIFINFO gif_info;
    if (GIF_getInfo(decoder->gif_image, &gif_info) == GIF_SUCCESS) {
        decoder->frame_count = gif_info.iFrameCount;
    } else {
        decoder->frame_count = 1; // 至少有一帧
    }

    // 分配帧缓冲区
    decoder->frame_buffer_size = decoder->width * decoder->height * sizeof(uint16_t);
    decoder->frame_buffer = (uint16_t*)heap_caps_malloc(decoder->frame_buffer_size, MALLOC_CAP_INTERNAL);
    if (!decoder->frame_buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        GIF_close(decoder->gif_image);
        free(decoder->gif_image);
        free(decoder);
        return NULL;
    }

    decoder->current_frame = 0;
    decoder->is_initialized = true;

    ESP_LOGI(TAG, "GIF decoder created: %lux%lu, %lu frames",
             decoder->width, decoder->height, decoder->frame_count);

    return decoder;
}

void gif_decoder_destroy(gif_decoder_t* decoder) {
    if (!decoder) {
        return;
    }

    if (decoder->gif_image) {
        GIF_close(decoder->gif_image);
        free(decoder->gif_image);
    }

    if (decoder->frame_buffer) {
        free(decoder->frame_buffer);
    }

    free(decoder);
    ESP_LOGI(TAG, "GIF decoder destroyed");
}

esp_err_t gif_decoder_get_info(gif_decoder_t* decoder, uint32_t* width,
                              uint32_t* height, uint32_t* frame_count) {
    if (!decoder || !decoder->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    if (width) *width = decoder->width;
    if (height) *height = decoder->height;
    if (frame_count) *frame_count = decoder->frame_count;

    return ESP_OK;
}

esp_err_t gif_decoder_decode_frame(gif_decoder_t* decoder, uint32_t frame_index,
                                  uint16_t* output_buffer, uint32_t buffer_size,
                                  uint32_t* delay_ms) {
    if (!decoder || !decoder->is_initialized || !output_buffer || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    if (frame_index >= decoder->frame_count) {
        return ESP_ERR_INVALID_ARG;
    }

    uint32_t required_size = decoder->width * decoder->height * sizeof(uint16_t);
    if (buffer_size < required_size) {
        return ESP_ERR_INVALID_SIZE;
    }

    // 如果需要解码不同的帧，重置到开始位置
    if (frame_index < decoder->current_frame) {
        GIF_reset(decoder->gif_image);
        decoder->current_frame = 0;
    }

    // 解码到目标帧
    while (decoder->current_frame <= frame_index) {
        int delay_ms_int = 0;
        int result = GIF_playFrame(decoder->gif_image, &delay_ms_int, decoder);

        if (result != GIF_SUCCESS) {
            if (result == GIF_EARLY_EOF && decoder->current_frame > 0) {
                // 到达文件末尾，重置到开始
                GIF_reset(decoder->gif_image);
                decoder->current_frame = 0;
                continue;
            } else {
                ESP_LOGE(TAG, "Failed to decode frame %lu, error: %d", frame_index, result);
                return ESP_FAIL;
            }
        }

        decoder->last_delay_ms = (delay_ms_int > 0) ? delay_ms_int : 100;

        if (decoder->current_frame == frame_index) {
            // 复制解码的帧数据到输出缓冲区
            memcpy(output_buffer, decoder->frame_buffer, required_size);
            *delay_ms = decoder->last_delay_ms;

            ESP_LOGD(TAG, "Decoded frame %lu: %lux%lu, delay=%lu ms",
                     frame_index, decoder->width, decoder->height, *delay_ms);

            decoder->current_frame++;
            return ESP_OK;
        }

        decoder->current_frame++;
    }

    return ESP_FAIL;
}

esp_err_t gif_decoder_reset(gif_decoder_t* decoder) {
    if (!decoder || !decoder->is_initialized) {
        return ESP_ERR_INVALID_ARG;
    }

    GIF_reset(decoder->gif_image);
    decoder->current_frame = 0;

    ESP_LOGD(TAG, "GIF decoder reset");
    return ESP_OK;
}
