#include "gif_decoder.h"
#include <esp_log.h>
#include <string.h>
#include <stdlib.h>

static const char* TAG = "GifDecoder";

// GIF文件头标识
#define GIF_SIGNATURE "GIF"
#define GIF87A_SIGNATURE "GIF87a"
#define GIF89A_SIGNATURE "GIF89a"

// GIF块类型
#define GIF_IMAGE_SEPARATOR     0x2C
#define GIF_EXTENSION_INTRODUCER 0x21
#define GIF_TRAILER             0x3B
#define GIF_GRAPHIC_CONTROL_LABEL 0xF9

/**
 * @brief GIF解码器内部上下文
 */
typedef struct {
    const uint8_t* data;               // GIF数据指针
    uint32_t data_size;                // 数据大小
    uint32_t position;                 // 当前读取位置
    
    // GIF头信息
    uint16_t screen_width;             // 屏幕宽度
    uint16_t screen_height;            // 屏幕高度
    uint8_t global_color_table_flag;   // 全局颜色表标志
    uint8_t color_resolution;          // 颜色分辨率
    uint8_t sort_flag;                 // 排序标志
    uint8_t global_color_table_size;   // 全局颜色表大小
    uint8_t background_color_index;    // 背景颜色索引
    uint8_t pixel_aspect_ratio;        // 像素宽高比
    
    // 帧信息
    uint32_t frame_count;              // 总帧数
    uint32_t* frame_positions;         // 帧位置数组
    uint32_t* frame_delays;            // 帧延迟数组
} gif_context_t;

// 读取16位小端序数据
static uint16_t read_uint16_le(const uint8_t* data) {
    return data[0] | (data[1] << 8);
}

// 跳过数据块
static esp_err_t skip_data_blocks(gif_context_t* ctx) {
    while (ctx->position < ctx->data_size) {
        uint8_t block_size = ctx->data[ctx->position++];
        if (block_size == 0) {
            break; // 块结束
        }
        ctx->position += block_size;
        if (ctx->position > ctx->data_size) {
            return ESP_ERR_INVALID_DATA;
        }
    }
    return ESP_OK;
}

// 解析GIF头
static esp_err_t parse_gif_header(gif_context_t* ctx) {
    if (ctx->data_size < 13) {
        ESP_LOGE(TAG, "GIF data too small for header");
        return ESP_ERR_INVALID_DATA;
    }
    
    // 检查GIF签名
    if (memcmp(ctx->data, GIF87A_SIGNATURE, 6) != 0 && 
        memcmp(ctx->data, GIF89A_SIGNATURE, 6) != 0) {
        ESP_LOGE(TAG, "Invalid GIF signature");
        return ESP_ERR_INVALID_DATA;
    }
    
    ctx->position = 6;
    
    // 读取逻辑屏幕描述符
    ctx->screen_width = read_uint16_le(&ctx->data[ctx->position]);
    ctx->position += 2;
    ctx->screen_height = read_uint16_le(&ctx->data[ctx->position]);
    ctx->position += 2;
    
    uint8_t packed = ctx->data[ctx->position++];
    ctx->global_color_table_flag = (packed >> 7) & 1;
    ctx->color_resolution = (packed >> 4) & 7;
    ctx->sort_flag = (packed >> 3) & 1;
    ctx->global_color_table_size = packed & 7;
    
    ctx->background_color_index = ctx->data[ctx->position++];
    ctx->pixel_aspect_ratio = ctx->data[ctx->position++];
    
    // 跳过全局颜色表
    if (ctx->global_color_table_flag) {
        uint32_t color_table_size = 3 * (1 << (ctx->global_color_table_size + 1));
        ctx->position += color_table_size;
    }
    
    ESP_LOGI(TAG, "GIF header parsed: %dx%d, global_color_table: %d",
             ctx->screen_width, ctx->screen_height, ctx->global_color_table_flag);
    
    return ESP_OK;
}

// 扫描GIF帧
static esp_err_t scan_gif_frames(gif_context_t* ctx) {
    uint32_t frame_count = 0;
    uint32_t max_frames = 100; // 最大帧数限制
    
    // 临时数组存储帧信息
    uint32_t* temp_positions = malloc(max_frames * sizeof(uint32_t));
    uint32_t* temp_delays = malloc(max_frames * sizeof(uint32_t));
    
    if (!temp_positions || !temp_delays) {
        free(temp_positions);
        free(temp_delays);
        return ESP_ERR_NO_MEM;
    }
    
    uint32_t current_delay = 100; // 默认延迟100ms
    
    while (ctx->position < ctx->data_size) {
        uint8_t separator = ctx->data[ctx->position++];
        
        if (separator == GIF_EXTENSION_INTRODUCER) {
            // 扩展块
            uint8_t label = ctx->data[ctx->position++];
            
            if (label == GIF_GRAPHIC_CONTROL_LABEL) {
                // 图形控制扩展
                uint8_t block_size = ctx->data[ctx->position++];
                if (block_size >= 4) {
                    ctx->position++; // 跳过packed字段
                    uint16_t delay = read_uint16_le(&ctx->data[ctx->position]);
                    ctx->position += 2;
                    current_delay = delay * 10; // 转换为毫秒
                    if (current_delay == 0) {
                        current_delay = 100; // 默认延迟
                    }
                    ctx->position++; // 跳过透明色索引
                }
                ctx->position++; // 跳过块终止符
            } else {
                // 其他扩展块，跳过
                skip_data_blocks(ctx);
            }
        } else if (separator == GIF_IMAGE_SEPARATOR) {
            // 图像数据块
            if (frame_count < max_frames) {
                temp_positions[frame_count] = ctx->position - 1;
                temp_delays[frame_count] = current_delay;
                frame_count++;
            }
            
            // 跳过图像描述符
            ctx->position += 8; // left, top, width, height
            uint8_t packed = ctx->data[ctx->position++];
            
            // 跳过局部颜色表
            if (packed & 0x80) {
                uint32_t local_color_table_size = 3 * (1 << ((packed & 7) + 1));
                ctx->position += local_color_table_size;
            }
            
            // 跳过LZW最小码长
            ctx->position++;
            
            // 跳过图像数据
            skip_data_blocks(ctx);
            
        } else if (separator == GIF_TRAILER) {
            // GIF结束
            break;
        } else {
            ESP_LOGW(TAG, "Unknown separator: 0x%02X at position %lu", separator, ctx->position - 1);
            break;
        }
    }
    
    // 分配实际需要的内存
    ctx->frame_count = frame_count;
    if (frame_count > 0) {
        ctx->frame_positions = malloc(frame_count * sizeof(uint32_t));
        ctx->frame_delays = malloc(frame_count * sizeof(uint32_t));
        
        if (ctx->frame_positions && ctx->frame_delays) {
            memcpy(ctx->frame_positions, temp_positions, frame_count * sizeof(uint32_t));
            memcpy(ctx->frame_delays, temp_delays, frame_count * sizeof(uint32_t));
        } else {
            ctx->frame_count = 0;
        }
    }
    
    free(temp_positions);
    free(temp_delays);
    
    ESP_LOGI(TAG, "Found %lu frames in GIF", frame_count);
    return ESP_OK;
}

gif_decoder_t* gif_decoder_create(const lv_img_dsc_t* gif_source) {
    if (!gif_source || !gif_source->data || gif_source->data_size == 0) {
        ESP_LOGE(TAG, "Invalid GIF source");
        return NULL;
    }
    
    gif_decoder_t* decoder = malloc(sizeof(gif_decoder_t));
    if (!decoder) {
        ESP_LOGE(TAG, "Failed to allocate decoder");
        return NULL;
    }
    
    gif_context_t* ctx = malloc(sizeof(gif_context_t));
    if (!ctx) {
        ESP_LOGE(TAG, "Failed to allocate context");
        free(decoder);
        return NULL;
    }
    
    memset(decoder, 0, sizeof(gif_decoder_t));
    memset(ctx, 0, sizeof(gif_context_t));
    
    // 初始化上下文
    ctx->data = gif_source->data;
    ctx->data_size = gif_source->data_size;
    ctx->position = 0;
    
    // 解析GIF头
    if (parse_gif_header(ctx) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to parse GIF header");
        free(ctx);
        free(decoder);
        return NULL;
    }
    
    // 扫描帧信息
    if (scan_gif_frames(ctx) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to scan GIF frames");
        free(ctx->frame_positions);
        free(ctx->frame_delays);
        free(ctx);
        free(decoder);
        return NULL;
    }
    
    // 设置解码器信息
    decoder->gif_source = gif_source;
    decoder->width = ctx->screen_width;
    decoder->height = ctx->screen_height;
    decoder->frame_count = ctx->frame_count;
    decoder->current_frame = 0;
    decoder->decoder_context = ctx;
    
    ESP_LOGI(TAG, "GIF decoder created: %dx%d, %lu frames",
             decoder->width, decoder->height, decoder->frame_count);

    return decoder;
}

void gif_decoder_destroy(gif_decoder_t* decoder) {
    if (!decoder) {
        return;
    }

    if (decoder->decoder_context) {
        gif_context_t* ctx = (gif_context_t*)decoder->decoder_context;
        free(ctx->frame_positions);
        free(ctx->frame_delays);
        free(ctx);
    }

    free(decoder);
}

esp_err_t gif_decoder_get_info(gif_decoder_t* decoder, uint32_t* width,
                              uint32_t* height, uint32_t* frame_count) {
    if (!decoder) {
        return ESP_ERR_INVALID_ARG;
    }

    if (width) *width = decoder->width;
    if (height) *height = decoder->height;
    if (frame_count) *frame_count = decoder->frame_count;

    return ESP_OK;
}

esp_err_t gif_decoder_decode_frame(gif_decoder_t* decoder, uint32_t frame_index,
                                  uint16_t* output_buffer, uint32_t buffer_size,
                                  uint32_t* delay_ms) {
    if (!decoder || !output_buffer || !delay_ms) {
        return ESP_ERR_INVALID_ARG;
    }

    gif_context_t* ctx = (gif_context_t*)decoder->decoder_context;
    if (!ctx || frame_index >= ctx->frame_count) {
        return ESP_ERR_INVALID_ARG;
    }

    uint32_t required_size = decoder->width * decoder->height * sizeof(uint16_t);
    if (buffer_size < required_size) {
        return ESP_ERR_INVALID_SIZE;
    }

    // 获取帧延迟
    *delay_ms = ctx->frame_delays[frame_index];

    // TODO: 实现真正的GIF帧解码
    // 目前使用模拟数据，生成基于帧索引的测试图案

    uint32_t pixel_count = decoder->width * decoder->height;
    for (uint32_t i = 0; i < pixel_count; i++) {
        // 生成测试图案：基于帧索引和像素位置的颜色
        uint32_t x = i % decoder->width;
        uint32_t y = i / decoder->width;

        // 创建彩色渐变效果
        uint8_t r = (frame_index * 8 + x / 8) & 0x1F;
        uint8_t g = (frame_index * 4 + y / 4) & 0x3F;
        uint8_t b = (frame_index * 16 + (x + y) / 16) & 0x1F;

        // 转换为RGB565格式
        uint16_t color = (r << 11) | (g << 5) | b;
        output_buffer[i] = color;
    }

    ESP_LOGD(TAG, "Decoded frame %lu: %lux%lu, delay=%lu ms",
             frame_index, decoder->width, decoder->height, *delay_ms);

    return ESP_OK;
}

esp_err_t gif_decoder_reset(gif_decoder_t* decoder) {
    if (!decoder) {
        return ESP_ERR_INVALID_ARG;
    }

    decoder->current_frame = 0;
    return ESP_OK;
}
