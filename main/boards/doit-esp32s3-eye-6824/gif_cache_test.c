#include "gif_cache_test.h"
#include "gif_frame_cache.h"
#include "gif_performance_monitor.h"
#include "eye_gif_resources.h"
#include <esp_log.h>
#include <esp_heap_caps.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <sys/time.h>
#include <string.h>

static const char* TAG = "GifCacheTest";

// 获取当前时间戳 (毫秒)
static uint32_t get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (tv.tv_sec * 1000) + (tv.tv_usec / 1000);
}

esp_err_t gif_cache_run_performance_test(uint32_t test_duration_ms, float target_fps, 
                                        gif_cache_test_result_t* result) {
    if (!result) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memset(result, 0, sizeof(gif_cache_test_result_t));
    result->test_duration_ms = test_duration_ms;
    
    ESP_LOGI(TAG, "Starting performance test (duration: %lu ms, target: %.1f FPS)", 
             test_duration_ms, target_fps);
    
    // 记录初始内存状态
    size_t initial_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t initial_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // 创建帧缓存管理器
    gif_frame_cache_t* cache = gif_frame_cache_create(&staticstate, 240, 240, 10, true);
    if (!cache) {
        strcpy(result->error_message, "Failed to create frame cache");
        return ESP_FAIL;
    }
    
    // 启动解码任务
    esp_err_t ret = gif_frame_cache_start_decode_task(cache);
    if (ret != ESP_OK) {
        strcpy(result->error_message, "Failed to start decode task");
        gif_frame_cache_destroy(cache);
        return ret;
    }
    
    // 预加载帧
    gif_frame_cache_preload_frames(cache, 0);
    
    // 创建性能监控器
    gif_performance_monitor_t* monitor = gif_performance_monitor_create(target_fps);
    if (!monitor) {
        strcpy(result->error_message, "Failed to create performance monitor");
        gif_frame_cache_destroy(cache);
        return ESP_FAIL;
    }
    
    gif_performance_monitor_start(monitor, false, 1000);
    
    // 运行测试
    uint32_t start_time = get_timestamp_ms();
    uint32_t frame_count = 0;
    uint32_t current_frame = 0;
    
    while ((get_timestamp_ms() - start_time) < test_duration_ms) {
        uint16_t* frame_data;
        uint32_t delay_ms;
        
        // 尝试获取帧数据
        esp_err_t frame_ret = gif_frame_cache_get_frame(cache, current_frame, &frame_data, &delay_ms);
        if (frame_ret == ESP_OK) {
            // 模拟帧显示
            gif_performance_monitor_record_frame(monitor);
            frame_count++;
            
            // 等待帧延迟
            vTaskDelay(pdMS_TO_TICKS(delay_ms));
        } else {
            // 缓存未命中，等待一段时间
            vTaskDelay(pdMS_TO_TICKS(10));
        }
        
        current_frame = (current_frame + 1) % 50; // 假设50帧循环
    }
    
    // 获取测试结果
    const gif_performance_stats_t* stats = gif_performance_monitor_get_stats(monitor);
    const gif_cache_stats_t* cache_stats = gif_frame_cache_get_stats(cache);
    
    result->achieved_fps = stats->average_fps;
    result->cache_hit_rate = cache_stats->cache_hit_rate;
    result->avg_decode_time_ms = (uint32_t)cache_stats->avg_decode_time_ms;
    
    // 计算内存使用
    size_t final_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t final_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    result->memory_used_internal = initial_internal - final_internal;
    result->memory_used_psram = initial_psram - final_psram;
    
    // 评估测试结果
    result->test_passed = (result->achieved_fps >= target_fps * 0.8f) && 
                         (result->cache_hit_rate >= 70.0f);
    
    if (!result->test_passed) {
        snprintf(result->error_message, sizeof(result->error_message),
                "Performance target not met: FPS=%.1f (target=%.1f), Cache=%.1f%%",
                result->achieved_fps, target_fps, result->cache_hit_rate);
    }
    
    // 清理资源
    gif_performance_monitor_destroy(monitor);
    gif_frame_cache_destroy(cache);
    
    ESP_LOGI(TAG, "Performance test completed: %s", result->test_passed ? "PASSED" : "FAILED");
    return ESP_OK;
}

esp_err_t gif_cache_run_memory_stress_test(gif_cache_test_result_t* result) {
    if (!result) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memset(result, 0, sizeof(gif_cache_test_result_t));
    
    ESP_LOGI(TAG, "Starting memory stress test");
    
    // 记录初始内存状态
    size_t initial_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t initial_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // 创建多个缓存管理器来测试内存压力
    gif_frame_cache_t* caches[3] = {NULL};
    int created_caches = 0;
    
    for (int i = 0; i < 3; i++) {
        caches[i] = gif_frame_cache_create(&staticstate, 240, 240, 10, true);
        if (caches[i]) {
            created_caches++;
            gif_frame_cache_start_decode_task(caches[i]);
        } else {
            ESP_LOGW(TAG, "Failed to create cache %d", i);
            break;
        }
    }
    
    if (created_caches == 0) {
        strcpy(result->error_message, "Failed to create any caches");
        return ESP_FAIL;
    }
    
    // 运行内存压力测试
    uint32_t start_time = get_timestamp_ms();
    bool memory_stable = true;
    
    for (int cycle = 0; cycle < 100 && memory_stable; cycle++) {
        for (int i = 0; i < created_caches; i++) {
            if (caches[i]) {
                uint16_t* frame_data;
                uint32_t delay_ms;
                gif_frame_cache_get_frame(caches[i], cycle % 50, &frame_data, &delay_ms);
            }
        }
        
        // 检查内存是否稳定
        size_t current_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        if (current_internal < 10000) { // 少于10KB内部RAM
            memory_stable = false;
            strcpy(result->error_message, "Internal RAM exhausted");
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    result->test_duration_ms = get_timestamp_ms() - start_time;
    
    // 计算内存使用
    size_t final_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t final_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    result->memory_used_internal = initial_internal - final_internal;
    result->memory_used_psram = initial_psram - final_psram;
    
    result->test_passed = memory_stable && (created_caches >= 2);
    
    // 清理资源
    for (int i = 0; i < created_caches; i++) {
        if (caches[i]) {
            gif_frame_cache_destroy(caches[i]);
        }
    }
    
    ESP_LOGI(TAG, "Memory stress test completed: %s", result->test_passed ? "PASSED" : "FAILED");
    return ESP_OK;
}

esp_err_t gif_cache_run_cache_efficiency_test(gif_cache_test_result_t* result) {
    if (!result) {
        return ESP_ERR_INVALID_ARG;
    }

    memset(result, 0, sizeof(gif_cache_test_result_t));

    ESP_LOGI(TAG, "Starting cache efficiency test");

    // 创建帧缓存管理器
    gif_frame_cache_t* cache = gif_frame_cache_create(&staticstate, 240, 240, 10, true);
    if (!cache) {
        strcpy(result->error_message, "Failed to create frame cache");
        return ESP_FAIL;
    }

    gif_frame_cache_start_decode_task(cache);
    gif_frame_cache_preload_frames(cache, 0);

    // 等待预加载完成
    vTaskDelay(pdMS_TO_TICKS(2000));

    uint32_t start_time = get_timestamp_ms();
    uint32_t total_accesses = 0;
    uint32_t cache_hits = 0;

    // 测试顺序访问 (应该有高命中率)
    for (int cycle = 0; cycle < 5; cycle++) {
        for (int frame = 0; frame < 10; frame++) {
            uint16_t* frame_data;
            uint32_t delay_ms;
            esp_err_t ret = gif_frame_cache_get_frame(cache, frame, &frame_data, &delay_ms);
            total_accesses++;
            if (ret == ESP_OK) {
                cache_hits++;
            }
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }

    // 测试随机访问 (命中率会降低)
    for (int i = 0; i < 50; i++) {
        uint32_t random_frame = (get_timestamp_ms() * 7) % 50; // 简单的伪随机
        uint16_t* frame_data;
        uint32_t delay_ms;
        esp_err_t ret = gif_frame_cache_get_frame(cache, random_frame, &frame_data, &delay_ms);
        total_accesses++;
        if (ret == ESP_OK) {
            cache_hits++;
        }
        vTaskDelay(pdMS_TO_TICKS(20));
    }

    result->test_duration_ms = get_timestamp_ms() - start_time;
    result->cache_hit_rate = total_accesses > 0 ? (float)cache_hits / total_accesses * 100.0f : 0.0f;

    const gif_cache_stats_t* cache_stats = gif_frame_cache_get_stats(cache);
    result->avg_decode_time_ms = (uint32_t)cache_stats->avg_decode_time_ms;

    // 缓存效率测试通过条件：命中率 > 60%
    result->test_passed = (result->cache_hit_rate >= 60.0f);

    if (!result->test_passed) {
        snprintf(result->error_message, sizeof(result->error_message),
                "Cache efficiency too low: %.1f%% (expected >= 60%%)",
                result->cache_hit_rate);
    }

    gif_frame_cache_destroy(cache);

    ESP_LOGI(TAG, "Cache efficiency test completed: %s", result->test_passed ? "PASSED" : "FAILED");
    return ESP_OK;
}

esp_err_t gif_cache_run_full_test_suite(void) {
    ESP_LOGI(TAG, "=== Starting GIF Cache Test Suite ===");

    gif_cache_test_result_t result;
    bool all_tests_passed = true;

    // 1. 性能测试
    ESP_LOGI(TAG, "\n--- Test 1: Performance Test ---");
    if (gif_cache_run_performance_test(10000, 20.0f, &result) == ESP_OK) {
        gif_cache_print_test_result(&result, "Performance Test");
        all_tests_passed &= result.test_passed;
    } else {
        ESP_LOGE(TAG, "Performance test failed to run");
        all_tests_passed = false;
    }

    // 2. 内存压力测试
    ESP_LOGI(TAG, "\n--- Test 2: Memory Stress Test ---");
    if (gif_cache_run_memory_stress_test(&result) == ESP_OK) {
        gif_cache_print_test_result(&result, "Memory Stress Test");
        all_tests_passed &= result.test_passed;
    } else {
        ESP_LOGE(TAG, "Memory stress test failed to run");
        all_tests_passed = false;
    }

    // 3. 缓存效率测试
    ESP_LOGI(TAG, "\n--- Test 3: Cache Efficiency Test ---");
    if (gif_cache_run_cache_efficiency_test(&result) == ESP_OK) {
        gif_cache_print_test_result(&result, "Cache Efficiency Test");
        all_tests_passed &= result.test_passed;
    } else {
        ESP_LOGE(TAG, "Cache efficiency test failed to run");
        all_tests_passed = false;
    }

    // 总结
    ESP_LOGI(TAG, "\n=== Test Suite Summary ===");
    ESP_LOGI(TAG, "Overall Result: %s", all_tests_passed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED");

    return all_tests_passed ? ESP_OK : ESP_FAIL;
}

void gif_cache_print_test_result(const gif_cache_test_result_t* result, const char* test_name) {
    if (!result || !test_name) {
        return;
    }

    ESP_LOGI(TAG, "=== %s Results ===", test_name);
    ESP_LOGI(TAG, "Status: %s", result->test_passed ? "✅ PASSED" : "❌ FAILED");
    ESP_LOGI(TAG, "Duration: %lu ms", result->test_duration_ms);

    if (result->achieved_fps > 0) {
        ESP_LOGI(TAG, "Achieved FPS: %.1f", result->achieved_fps);
    }

    if (result->cache_hit_rate > 0) {
        ESP_LOGI(TAG, "Cache Hit Rate: %.1f%%", result->cache_hit_rate);
    }

    if (result->avg_decode_time_ms > 0) {
        ESP_LOGI(TAG, "Avg Decode Time: %lu ms", result->avg_decode_time_ms);
    }

    ESP_LOGI(TAG, "Memory Usage - Internal: %zu KB, PSRAM: %zu KB",
             result->memory_used_internal / 1024, result->memory_used_psram / 1024);

    if (strlen(result->error_message) > 0) {
        ESP_LOGI(TAG, "Error: %s", result->error_message);
    }
}
