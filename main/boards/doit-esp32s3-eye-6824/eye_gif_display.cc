#include "eye_gif_display.h"
#include "gif_config.h"
#include "config.h"
#include <esp_log.h>
#include <algorithm>
#include <cstring>
#include <string>
#include <lvgl.h>
#include <esp_heap_caps.h>
#include "display/lcd_display.h"
#include "font_awesome_symbols.h"
#include "eye_gif_resources.h"
#include "esp_lvgl_port.h"
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <freertos/task.h>

#define TAG "EyeGifDisplay"

// 内存使用情况检查函数
static void log_memory_usage() {
    size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_DEFAULT);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    size_t min_free_psram = heap_caps_get_minimum_free_size(MALLOC_CAP_SPIRAM);

    ESP_LOGI(TAG, "Memory usage:");
    ESP_LOGI(TAG, "  Internal RAM - Free: %zu bytes, Min free: %zu bytes", free_internal, min_free_internal);
    ESP_LOGI(TAG, "  PSRAM - Free: %zu bytes, Min free: %zu bytes", free_psram, min_free_psram);
    ESP_LOGI(TAG, "  Total heap - Free: %zu bytes", free_heap);

    // 计算缓冲区使用的内存
    uint32_t buffer_size = DISPLAY_WIDTH * DISPLAY_HEIGHT / GIF_BUFFER_DIVISOR * 2; // 像素 * 2字节
    uint32_t total_buffer_memory = buffer_size * (GIF_ENABLE_DOUBLE_BUFFER ? 2 : 1) * 2; // 双屏
    ESP_LOGI(TAG, "  Buffer config: %dx%d/%d = %lu pixels per buffer",
             DISPLAY_WIDTH, DISPLAY_HEIGHT, GIF_BUFFER_DIVISOR, (uint32_t)(DISPLAY_WIDTH * DISPLAY_HEIGHT / GIF_BUFFER_DIVISOR));
    ESP_LOGI(TAG, "  Estimated buffer memory: %lu bytes (%s, %s)",
             total_buffer_memory,
             GIF_USE_PSRAM_BUFFER ? "PSRAM" : "Internal",
             GIF_ENABLE_DOUBLE_BUFFER ? "double-buffered" : "single-buffered");

    // 检查内存泄漏迹象
    static size_t last_free_internal = 0;
    if (last_free_internal > 0) {
        int32_t diff = (int32_t)free_internal - (int32_t)last_free_internal;
        if (diff < -1000) { // 如果减少超过1KB
            ESP_LOGW(TAG, "  ⚠️  Internal RAM decreased by %ld bytes since last check", -diff);
        } else if (diff > 1000) {
            ESP_LOGI(TAG, "  ✅ Internal RAM increased by %ld bytes since last check", diff);
        }
    }
    last_free_internal = free_internal;

    // 获取各种内存池的详细信息
    size_t free_8bit = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t free_32bit = heap_caps_get_free_size(MALLOC_CAP_32BIT);
    size_t free_dma = heap_caps_get_free_size(MALLOC_CAP_DMA);
    ESP_LOGI(TAG, "  Memory pools - 8bit: %zu, 32bit: %zu, DMA: %zu bytes",
             free_8bit, free_32bit, free_dma);
}

// 表情映射表 - 将各种表情映射到src目录下的实际GIF资源
const EyeGifDisplay::EmotionMap EyeGifDisplay::emotion_maps_[] = {
    // 中性/平静类表情 -> staticstate
    {"neutral", &staticstate},
    {"relaxed", &staticstate},
    {"sleepy", &staticstate},

    // 积极/开心类表情 -> happy
    {"happy", &happy},
    {"laughing", &happy},
    {"funny", &happy},
    {"loving", &cute},
    {"confident", &happy},
    {"winking", &moving},
    {"cool", &happy},
    {"delicious", &cute},
    {"kissy", &cute},
    {"silly", &happy},

    // 悲伤类表情 -> sad
    {"sad", &sad},
    {"crying", &sad},

    // 愤怒类表情 -> anger
    {"angry", &scare},

    // 惊讶类表情 -> scare
    {"surprised", &scare},
    {"shocked", &scare},

    // 思考/困惑类表情 -> buxue
    {"thinking", &moving},
    {"confused", &buxue},
    {"embarrassed", &buxue},

    // 系统状态也映射到现有表情
    {"loading", &buxue},        // 加载中用思考表情
    {"listening", &staticstate}, // 听取中用静态表情
    {"speaking", &happy},       // 说话中用开心表情
    {"error", &anger},          // 错误用愤怒表情

    {"purple_left",&purple_eye_240_240},
    {"purple_right",&purple_eye_240_240_ro},
    {"heart_beat",&heart_beat},

    {nullptr, nullptr}  // 结束标记
};

EyeGifDisplay::EyeGifDisplay(int width, int height, int offset_x, int offset_y, bool mirror_x,
                             bool mirror_y, bool swap_xy, DisplayFonts fonts)
    : Display(),
      width_(width), height_(height), offset_x_(offset_x), offset_y_(offset_y),
      mirror_x_(mirror_x), mirror_y_(mirror_y), swap_xy_(swap_xy), fonts_(fonts) {
    ESP_LOGI(TAG, "EyeGifDisplay dual screen constructor called");

    // 创建互斥锁
    mutex_ = xSemaphoreCreateMutex();
    if (mutex_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return;
    }

    // 创建GIF同步队列
    gif_sync_queue_ = xQueueCreate(GIF_SYNC_QUEUE_SIZE, sizeof(GifSyncMessage));
    if (gif_sync_queue_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create GIF sync queue");
        return;
    }

    // 获取外部屏幕句柄
    extern esp_lcd_panel_io_handle_t lcd_io_eye;
    extern esp_lcd_panel_handle_t lcd_panel_eye;
    extern esp_lcd_panel_io_handle_t lcd_io_eye2;
    extern esp_lcd_panel_handle_t lcd_panel_eye2;

    ESP_LOGI(TAG, "=== External handles debug ===");
    ESP_LOGI(TAG, "lcd_io_eye: %p, lcd_panel_eye: %p", lcd_io_eye, lcd_panel_eye);
    ESP_LOGI(TAG, "lcd_io_eye2: %p, lcd_panel_eye2: %p", lcd_io_eye2, lcd_panel_eye2);

    panel_io1_ = lcd_io_eye;
    panel1_ = lcd_panel_eye;
    panel_io2_ = lcd_io_eye2;
    panel2_ = lcd_panel_eye2;

    ESP_LOGI(TAG, "=== Assigned handles debug ===");
    ESP_LOGI(TAG, "panel_io1_: %p, panel1_: %p", panel_io1_, panel1_);
    ESP_LOGI(TAG, "panel_io2_: %p, panel2_: %p", panel_io2_, panel2_);

    // 验证屏幕句柄
    if (!panel_io1_ || !panel1_) {
        ESP_LOGE(TAG, "Screen 1 handles are invalid - panel_io1_: %p, panel1_: %p", panel_io1_, panel1_);
        return;
    }
    if (!panel_io2_ || !panel2_) {
        ESP_LOGE(TAG, "Screen 2 handles are invalid - panel_io2_: %p, panel2_: %p", panel_io2_, panel2_);
        return;
    }

    ESP_LOGI(TAG, "All screen handles validated successfully");

    // 初始化LVGL和双屏显示
    InitializeLVGL();
    InitializeDualScreenDisplays();
    SetupDualScreenGifContainers();

    // 调试 LVGL 配置
    DebugLVGLDisplays();

    // 启动GIF同步任务
    StartGifSyncTask();

#if GIF_FRAME_CACHE_ENABLED
    // 初始化帧缓存
    InitializeFrameCache();
#endif

    ESP_LOGI(TAG, "EyeGifDisplay initialization completed");
}

EyeGifDisplay::~EyeGifDisplay() {
    ESP_LOGI(TAG, "EyeGifDisplay destructor called");

    // 清理LVGL对象
    if (emotion_gif1_) {
        lv_obj_del(emotion_gif1_);
        emotion_gif1_ = nullptr;
    }
    if (emotion_gif2_) {
        lv_obj_del(emotion_gif2_);
        emotion_gif2_ = nullptr;
    }
    if (screen1_) {
        lv_obj_del(screen1_);
        screen1_ = nullptr;
    }
    if (screen2_) {
        lv_obj_del(screen2_);
        screen2_ = nullptr;
    }
    if (display1_) {
        lv_display_delete(display1_);
        display1_ = nullptr;
    }
    if (display2_) {
        lv_display_delete(display2_);
        display2_ = nullptr;
    }

    // 停止GIF同步任务
    StopGifSyncTask();

#if GIF_FRAME_CACHE_ENABLED
    // 清理帧缓存
    DestroyFrameCache();
#endif

    // 清理GIF同步队列
    if (gif_sync_queue_) {
        vQueueDelete(gif_sync_queue_);
        gif_sync_queue_ = nullptr;
    }

    // 清理互斥锁
    if (mutex_) {
        vSemaphoreDelete(mutex_);
        mutex_ = nullptr;
    }
}

void EyeGifDisplay::InitializeLVGL() {
    ESP_LOGI(TAG, "Initializing LVGL library");
    lv_init();

    ESP_LOGI(TAG, "Initializing LVGL port with optimized settings");
    lvgl_port_cfg_t port_cfg = ESP_LVGL_PORT_INIT_CONFIG();

    // 优化LVGL性能设置
    port_cfg.task_priority = GIF_LVGL_TASK_PRIORITY;        // 提高任务优先级
    port_cfg.timer_period_ms = GIF_LVGL_TIMER_PERIOD_MS;    // 约60FPS刷新率 (1000/60≈16ms)
    port_cfg.task_stack = GIF_LVGL_TASK_STACK;              // 增加堆栈大小
    port_cfg.task_affinity = GIF_LVGL_TASK_CORE;            // 绑定到核心1，让核心0处理其他任务

    esp_err_t ret = lvgl_port_init(&port_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize LVGL port: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "LVGL port initialized with %dFPS target on core %d",
             1000/GIF_LVGL_TIMER_PERIOD_MS, GIF_LVGL_TASK_CORE);

    // 设置LVGL的性能优化选项
    lv_disp_set_default(nullptr); // 清除默认显示，避免冲突
}

// 创建通用的显示配置
lvgl_port_display_cfg_t EyeGifDisplay::CreateDisplayConfig(esp_lcd_panel_io_handle_t io_handle,
                                                          esp_lcd_panel_handle_t panel_handle) {
    return {
        .io_handle = io_handle,
        .panel_handle = panel_handle,
        .control_handle = nullptr,
        .buffer_size = static_cast<uint32_t>(width_ * height_ / GIF_BUFFER_DIVISOR),
        .double_buffer = GIF_ENABLE_DOUBLE_BUFFER,
        .trans_size = 0,
        .hres = static_cast<uint32_t>(width_),
        .vres = static_cast<uint32_t>(height_),
        .monochrome = false,
        .rotation = {
            .swap_xy = swap_xy_,
            .mirror_x = mirror_x_,
            .mirror_y = mirror_y_,
        },
        .color_format = LV_COLOR_FORMAT_RGB565,
        .flags = {
            .buff_dma = GIF_USE_DMA_BUFFER ? 1 : 0,
            .buff_spiram = GIF_USE_PSRAM_BUFFER ? 1 : 0,
            .sw_rotate = 0,
            .swap_bytes = 1,
            .full_refresh = 0,
            .direct_mode = 0,
        },
    };
}

// 创建并配置显示器
lv_display_t* EyeGifDisplay::CreateAndConfigureDisplay(const lvgl_port_display_cfg_t& config,
                                                      const char* display_name) {
    ESP_LOGI(TAG, "Adding LVGL %s with panel_handle: %p", display_name, config.panel_handle);

    lv_display_t* display = lvgl_port_add_disp(&config);
    ESP_LOGI(TAG, "%s created: %p", display_name, display);

    if (display == nullptr) {
        ESP_LOGE(TAG, "Failed to add LVGL %s", display_name);
        return nullptr;
    }

    // 设置偏移量（如果需要）
    if (offset_x_ != 0 || offset_y_ != 0) {
        lv_display_set_offset(display, offset_x_, offset_y_);
        ESP_LOGI(TAG, "%s offset set to (%d, %d)", display_name, offset_x_, offset_y_);
    }

    return display;
}

void EyeGifDisplay::InitializeDualScreenDisplays() {
    ESP_LOGI(TAG, "=== Initializing dual screen displays ===");
    ESP_LOGI(TAG, "Panel handles - panel1_: %p, panel2_: %p", panel1_, panel2_);
    ESP_LOGI(TAG, "Panel IO handles - panel_io1_: %p, panel_io2_: %p", panel_io1_, panel_io2_);

    // 显示缓冲配置信息
    uint32_t buffer_size = width_ * height_ / GIF_BUFFER_DIVISOR;
    ESP_LOGI(TAG, "Buffer configuration:");
    ESP_LOGI(TAG, "  - Buffer size: %lu pixels (1/%d of screen)", buffer_size, GIF_BUFFER_DIVISOR);
    ESP_LOGI(TAG, "  - Double buffer: %s", GIF_ENABLE_DOUBLE_BUFFER ? "enabled" : "disabled");
    ESP_LOGI(TAG, "  - DMA buffer: %s", GIF_USE_DMA_BUFFER ? "enabled" : "disabled");
    ESP_LOGI(TAG, "  - PSRAM buffer: %s", GIF_USE_PSRAM_BUFFER ? "enabled" : "disabled");

    // 批量创建两个屏幕
    struct DisplayInfo {
        esp_lcd_panel_io_handle_t io_handle;
        esp_lcd_panel_handle_t panel_handle;
        lv_display_t** display_ptr;
        const char* name;
    };

    DisplayInfo displays[] = {
        {panel_io1_, panel1_, &display1_, "display 1"},
        {panel_io2_, panel2_, &display2_, "display 2"}
    };

    for (auto& disp_info : displays) {
        lvgl_port_display_cfg_t config = CreateDisplayConfig(disp_info.io_handle, disp_info.panel_handle);
        *(disp_info.display_ptr) = CreateAndConfigureDisplay(config, disp_info.name);
        if (*(disp_info.display_ptr) == nullptr) {
            ESP_LOGE(TAG, "Failed to initialize %s", disp_info.name);
            return;
        }
    }

    ESP_LOGI(TAG, "=== Dual screen displays initialized successfully ===");
    ESP_LOGI(TAG, "Display1: %p, Display2: %p", display1_, display2_);

    // 验证显示对象的分辨率
    if (display1_) {
        ESP_LOGI(TAG, "Display1 resolution: %lux%lu",
                 lv_display_get_horizontal_resolution(display1_),
                 lv_display_get_vertical_resolution(display1_));
    }

    if (display2_) {
        ESP_LOGI(TAG, "Display2 resolution: %lux%lu",
                 lv_display_get_horizontal_resolution(display2_),
                 lv_display_get_vertical_resolution(display2_));
    }
}

void EyeGifDisplay::SetupDualScreenGifContainers() {
    ESP_LOGI(TAG, "=== Setting up dual-screen GIF containers ===");
    ESP_LOGI(TAG, "Display objects - display1_: %p, display2_: %p", display1_, display2_);
    log_memory_usage();

    if (lvgl_port_lock(30000)) {
        ESP_LOGI(TAG, "LVGL port locked successfully");

        // 设置第一个屏幕
        ESP_LOGI(TAG, "Setting up screen 1...");
        ESP_LOGI(TAG, "Current default display before: %p", lv_display_get_default());
        lv_display_set_default(display1_);
        ESP_LOGI(TAG, "Set default display to display1_: %p", display1_);
        ESP_LOGI(TAG, "Current default display after: %p", lv_display_get_default());

        screen1_ = lv_obj_create(NULL);
        ESP_LOGI(TAG, "Created screen1_: %p", screen1_);
        lv_obj_set_style_bg_color(screen1_, lv_color_black(), 0);
        lv_screen_load(screen1_);
        ESP_LOGI(TAG, "Screen1 loaded and configured");

        emotion_gif1_ = lv_gif_create(screen1_);
        ESP_LOGI(TAG, "Created emotion_gif1_: %p on screen1_: %p", emotion_gif1_, screen1_);
        lv_obj_set_size(emotion_gif1_, width_, height_);
        lv_obj_set_style_border_width(emotion_gif1_, 0, 0);
        lv_obj_set_style_bg_opa(emotion_gif1_, LV_OPA_TRANSP, 0);
        lv_obj_center(emotion_gif1_);
        lv_gif_set_src(emotion_gif1_, &staticstate);
        ESP_LOGI(TAG, "emotion_gif1_ configured with size %dx%d", width_, height_);

        // 设置第二个屏幕
        ESP_LOGI(TAG, "Setting up screen 2...");
        ESP_LOGI(TAG, "Current default display before: %p", lv_display_get_default());
        lv_display_set_default(display2_);
        ESP_LOGI(TAG, "Set default display to display2_: %p", display2_);
        ESP_LOGI(TAG, "Current default display after: %p", lv_display_get_default());

        screen2_ = lv_obj_create(NULL);
        ESP_LOGI(TAG, "Created screen2_: %p", screen2_);
        lv_obj_set_style_bg_color(screen2_, lv_color_black(), 0);
        lv_screen_load(screen2_);
        ESP_LOGI(TAG, "Screen2 loaded and configured");

        emotion_gif2_ = lv_gif_create(screen2_);
        ESP_LOGI(TAG, "Created emotion_gif2_: %p on screen2_: %p", emotion_gif2_, screen2_);
        lv_obj_set_size(emotion_gif2_, width_, height_);
        lv_obj_set_style_border_width(emotion_gif2_, 0, 0);
        lv_obj_set_style_bg_opa(emotion_gif2_, LV_OPA_TRANSP, 0);
        lv_obj_center(emotion_gif2_);
        lv_gif_set_src(emotion_gif2_, &staticstate);
        ESP_LOGI(TAG, "emotion_gif2_ configured with size %dx%d", width_, height_);

        lvgl_port_unlock();
        ESP_LOGI(TAG, "LVGL port unlocked");
    } else {
        ESP_LOGE(TAG, "Failed to lock LVGL port");
    }

    ESP_LOGI(TAG, "=== Dual-screen GIF containers setup completed ===");
    ESP_LOGI(TAG, "Final objects - screen1_: %p, screen2_: %p", screen1_, screen2_);
    ESP_LOGI(TAG, "Final GIF objects - emotion_gif1_: %p, emotion_gif2_: %p", emotion_gif1_, emotion_gif2_);
    log_memory_usage();
}

bool EyeGifDisplay::Lock(int timeout_ms) {
    if (mutex_ == nullptr) {
        return false;
    }
    return xSemaphoreTake(mutex_, pdMS_TO_TICKS(timeout_ms)) == pdTRUE;
}

void EyeGifDisplay::Unlock() {
    if (mutex_ != nullptr) {
        xSemaphoreGive(mutex_);
    }
}

const lv_img_dsc_t* EyeGifDisplay::GetGifResource(const char* emotion) {
    if (!emotion) {
        return &staticstate;
    }

    // 查找对应的GIF资源
    for (const auto& map : emotion_maps_) {
        if (map.name && strcmp(map.name, emotion) == 0) {
            return map.gif ? map.gif : &staticstate;
        }
    }

    // 如果没找到对应表情，使用默认表情
    ESP_LOGW(TAG, "Unknown emotion '%s', using default", emotion);
    return &staticstate;
}

void EyeGifDisplay::SetGifOnScreen(uint8_t screen_id, const lv_img_dsc_t* gif_dsc) {
    ESP_LOGI(TAG, "=== SetGifOnScreen Debug (No Default Display Change) ===");
    ESP_LOGI(TAG, "Screen ID: %d, GIF descriptor: %p", screen_id, gif_dsc);
    ESP_LOGI(TAG, "emotion_gif1_: %p, emotion_gif2_: %p", emotion_gif1_, emotion_gif2_);
    ESP_LOGI(TAG, "display1_: %p, display2_: %p", display1_, display2_);

    if (!gif_dsc) {
        ESP_LOGE(TAG, "GIF descriptor is null");
        return;
    }

    ESP_LOGI(TAG, "Attempting to lock LVGL port...");
    if (lvgl_port_lock(30000)) {
        ESP_LOGI(TAG, "LVGL port locked successfully");
        ESP_LOGI(TAG, "Current default display (unchanged): %p", lv_display_get_default());

        if (screen_id == 1 && emotion_gif1_ && display1_) {
            ESP_LOGI(TAG, "Processing screen 1 (without changing default display)...");

            // 验证GIF对象的显示器归属
            lv_obj_t* gif_parent = lv_obj_get_parent(emotion_gif1_);
            ESP_LOGI(TAG, "emotion_gif1_ parent screen: %p (our screen1_: %p)", gif_parent, screen1_);

#if GIF_FRAME_CACHE_ENABLED
            // 更新帧缓存
            UpdateFrameCache(1, gif_dsc);
#endif

            // 直接设置GIF源，不改变默认显示
            ESP_LOGI(TAG, "Setting GIF source on emotion_gif1_: %p", emotion_gif1_);
            ESP_LOGI(TAG, "GIF data pointer: %p, size: %lu bytes", gif_dsc->data, gif_dsc->data_size);
            lv_gif_set_src(emotion_gif1_, gif_dsc);

            ESP_LOGI(TAG, "Forcing object invalidation for emotion_gif1_");
            lv_obj_invalidate(emotion_gif1_);

            ESP_LOGI(TAG, "Checking if GIF is visible and valid");
            ESP_LOGI(TAG, "emotion_gif1_ hidden flag: %d", lv_obj_has_flag(emotion_gif1_, LV_OBJ_FLAG_HIDDEN));

            ESP_LOGI(TAG, "GIF set on screen 1 completed");
        } else if (screen_id == 2 && emotion_gif2_ && display2_) {
            ESP_LOGI(TAG, "Processing screen 2 (without changing default display)...");

            // 验证GIF对象的显示器归属
            lv_obj_t* gif_parent = lv_obj_get_parent(emotion_gif2_);
            ESP_LOGI(TAG, "emotion_gif2_ parent screen: %p (our screen2_: %p)", gif_parent, screen2_);

#if GIF_FRAME_CACHE_ENABLED
            // 更新帧缓存
            UpdateFrameCache(2, gif_dsc);
#endif

            // 直接设置GIF源，不改变默认显示
            ESP_LOGI(TAG, "Setting GIF source on emotion_gif2_: %p", emotion_gif2_);
            ESP_LOGI(TAG, "GIF data pointer: %p, size: %lu bytes", gif_dsc->data, gif_dsc->data_size);
            lv_gif_set_src(emotion_gif2_, gif_dsc);

            ESP_LOGI(TAG, "Forcing object invalidation for emotion_gif2_");
            lv_obj_invalidate(emotion_gif2_);

            ESP_LOGI(TAG, "Checking if GIF is visible and valid");
            ESP_LOGI(TAG, "emotion_gif2_ hidden flag: %d", lv_obj_has_flag(emotion_gif2_, LV_OBJ_FLAG_HIDDEN));

            ESP_LOGI(TAG, "GIF set on screen 2 completed");
        } else {
            ESP_LOGE(TAG, "Invalid conditions - screen_id: %d", screen_id);
            ESP_LOGE(TAG, "emotion_gif1_: %p, display1_: %p", emotion_gif1_, display1_);
            ESP_LOGE(TAG, "emotion_gif2_: %p, display2_: %p", emotion_gif2_, display2_);
        }

        ESP_LOGI(TAG, "Default display remains: %p", lv_display_get_default());
        lvgl_port_unlock();
        ESP_LOGI(TAG, "LVGL port unlocked");
    } else {
        ESP_LOGE(TAG, "Failed to lock LVGL port");
    }

    ESP_LOGI(TAG, "=== SetGifOnScreen Debug End ===");
}

// 调试函数：检查 LVGL 显示配置
void EyeGifDisplay::DebugLVGLDisplays() {
    ESP_LOGI(TAG, "=== LVGL Display Configuration Debug ===");

    // 检查默认显示
    lv_display_t* default_disp = lv_display_get_default();
    ESP_LOGI(TAG, "Default display: %p", default_disp);

    // 检查显示列表
    lv_display_t* disp = lv_display_get_next(NULL);
    int count = 0;
    while (disp) {
        ESP_LOGI(TAG, "Display %d: %p", count, disp);
        if (disp == display1_) {
            ESP_LOGI(TAG, "  -> This is display1_");
        }
        if (disp == display2_) {
            ESP_LOGI(TAG, "  -> This is display2_");
        }
        ESP_LOGI(TAG, "  Resolution: %lux%lu",
                 lv_display_get_horizontal_resolution(disp),
                 lv_display_get_vertical_resolution(disp));

        disp = lv_display_get_next(disp);
        count++;
    }

    ESP_LOGI(TAG, "Total displays found: %d", count);
    ESP_LOGI(TAG, "Our display1_: %p, display2_: %p", display1_, display2_);

    // 检查屏幕对象
    ESP_LOGI(TAG, "Screen objects - screen1_: %p, screen2_: %p", screen1_, screen2_);
    ESP_LOGI(TAG, "GIF objects - emotion_gif1_: %p, emotion_gif2_: %p", emotion_gif1_, emotion_gif2_);

    // 检查当前活动屏幕
    if (display1_) {
        lv_display_set_default(display1_);
        lv_obj_t* active_screen1 = lv_screen_active();
        ESP_LOGI(TAG, "Active screen on display1_: %p (our screen1_: %p)", active_screen1, screen1_);
    }

    if (display2_) {
        lv_display_set_default(display2_);
        lv_obj_t* active_screen2 = lv_screen_active();
        ESP_LOGI(TAG, "Active screen on display2_: %p (our screen2_: %p)", active_screen2, screen2_);
    }

    ESP_LOGI(TAG, "=== LVGL Display Configuration Debug End ===");
}

void EyeGifDisplay::SetStatus(const char* status) {
    // 空实现 - 魔眼不显示状态文字
    ESP_LOGD(TAG, "SetStatus ignored - EyeGifDisplay does not show status text");
}

void EyeGifDisplay::SetTheme(const std::string& theme_name) {
    // 空实现 - 魔眼不支持主题切换
    ESP_LOGD(TAG, "SetTheme ignored - EyeGifDisplay does not support theme switching");
}

void EyeGifDisplay::SetEmotionOnScreen(uint8_t screen_id, const char* emotion) {
    if (!emotion) {
        ESP_LOGW(TAG, "SetEmotionOnScreen called with null emotion");
        return;
    }

    ESP_LOGI(TAG, "Setting emotion '%s' on screen %d", emotion, screen_id);

    const lv_img_dsc_t* gif_resource = GetGifResource(emotion);
    if (!gif_resource) {
        ESP_LOGE(TAG, "Failed to get GIF resource for emotion: %s", emotion);
        return;
    }

    // 使用同步任务来处理单屏GIF更新
    GifSyncMessage message = {
        .gif_resource = gif_resource,
        .sync_both_screens = false,
        .target_screen = screen_id
    };

    if (gif_sync_queue_ != nullptr) {
        if (xQueueSend(gif_sync_queue_, &message, pdMS_TO_TICKS(GIF_QUEUE_SEND_TIMEOUT_MS)) != pdTRUE) {
            ESP_LOGE(TAG, "Failed to send single screen message to queue");
        } else {
            ESP_LOGI(TAG, "Single screen message sent to queue successfully");
        }
    } else {
        ESP_LOGE(TAG, "GIF sync queue not initialized");
    }
}

void EyeGifDisplay::SetDualEmotion(const char* emotion1, const char* emotion2) {
    ESP_LOGI(TAG, "Setting dual emotions: screen1='%s', screen2='%s'",
             emotion1 ? emotion1 : "null", emotion2 ? emotion2 : "null");

    if (emotion1) {
        SetEmotionOnScreen(1, emotion1);
    }
    if (emotion2) {
        SetEmotionOnScreen(2, emotion2);
    }
}

void EyeGifDisplay::SetSyncEmotion(const char* emotion) {
    ESP_LOGI(TAG, "Setting sync emotion: '%s'", emotion ? emotion : "null");

    if (!emotion) {
        return;
    }

    const lv_img_dsc_t* gif_resource = GetGifResource(emotion);
    if (!gif_resource) {
        ESP_LOGE(TAG, "Failed to get GIF resource for emotion: %s", emotion);
        return;
    }

    // 使用同步任务来处理GIF更新
    GifSyncMessage message = {
        .gif_resource = gif_resource,
        .sync_both_screens = true,
        .target_screen = 0  // 忽略，因为sync_both_screens为true
    };

    if (gif_sync_queue_ != nullptr) {
        if (xQueueSend(gif_sync_queue_, &message, pdMS_TO_TICKS(GIF_QUEUE_SEND_TIMEOUT_MS)) != pdTRUE) {
            ESP_LOGE(TAG, "Failed to send sync message to queue");
        } else {
            ESP_LOGI(TAG, "Sync message sent to queue successfully");
        }
    } else {
        ESP_LOGE(TAG, "GIF sync queue not initialized");
    }
}

void EyeGifDisplay::SetEmotion(const char* emotion) {
    if (!emotion) {
        ESP_LOGW(TAG, "SetEmotion called with null emotion");
        return;
    }

    ESP_LOGI(TAG, "Setting emotion on both screens: %s", emotion);
    log_memory_usage();

    // 根据表情类型在两个屏幕上显示不同的资源
    // if (strcmp(emotion, "sad") == 0) {
    //     // 悲伤表情：屏幕1显示紫色眼睛，屏幕2显示悲伤表情
    //     SetEmotionOnScreen(1, "purple_left");
    //     SetEmotionOnScreen(2, "purple_right");
    // } else if (strcmp(emotion, "happy") == 0) {
    //     // 开心表情：屏幕1显示心跳，屏幕2显示开心表情
    //     SetEmotionOnScreen(1, "heart_beat");
    //     SetEmotionOnScreen(2, "happy");
    // } else {
        // 其他表情：两个屏幕显示相同内容
        SetSyncEmotion(emotion);
    // }

    log_memory_usage();
}

void EyeGifDisplay::SetChatMessage(const char* role, const char* content) {
    // 空实现 - 魔眼只显示 GIF，不显示文字消息
    ESP_LOGD(TAG, "SetChatMessage ignored - EyeGifDisplay only shows GIF animations");
}

void EyeGifDisplay::SetIcon(const char* icon) {
    if (!icon) {
        return;
    }

    // 根据图标类型设置对应的表情，但不显示文字
    if (strcmp(icon, FONT_AWESOME_DOWNLOAD) == 0) {
        SetEmotion("loading");
        ESP_LOGI(TAG, "Icon: %s -> loading emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_WIFI) == 0) {
        SetEmotion("neutral");
        ESP_LOGI(TAG, "Icon: %s -> neutral emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_MUSIC) == 0) {
        SetEmotion("listening");
        ESP_LOGI(TAG, "Icon: %s -> listening emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_VOLUME_HIGH) == 0) {
        SetEmotion("speaking");
        ESP_LOGI(TAG, "Icon: %s -> speaking emotion", icon);
    } else {
        SetEmotion("neutral");
        ESP_LOGI(TAG, "Icon: %s -> neutral emotion (default)", icon);
    }
}

// GIF同步任务实现 (优化栈使用)
void EyeGifDisplay::GifSyncTask(void* parameter) {
    EyeGifDisplay* display = static_cast<EyeGifDisplay*>(parameter);
    GifSyncMessage message;

    ESP_LOGI(TAG, "GIF sync task started on core %d", xPortGetCoreID());

    while (true) {
        // 等待GIF同步消息，使用较长超时确保同步功能
        if (xQueueReceive(display->gif_sync_queue_, &message, pdMS_TO_TICKS(5000)) == pdTRUE) {
            // 减少日志输出以节省栈空间
            #if GIF_DEBUG_SYNC_MESSAGES
            ESP_LOGI(TAG, "Processing GIF sync message - sync_both: %d, target_screen: %d",
                     message.sync_both_screens, message.target_screen);
            #endif

            if (!message.gif_resource) {
                ESP_LOGW(TAG, "Null GIF resource, skipping");
                continue;
            }

            // 获取LVGL锁，简化重试逻辑
            bool lock_acquired = false;
            for (int retry = 0; retry < 3 && !lock_acquired; retry++) {
                lock_acquired = lvgl_port_lock(GIF_LVGL_LOCK_TIMEOUT_MS);
                if (!lock_acquired) {
                    vTaskDelay(pdMS_TO_TICKS(50));
                }
            }

            if (lock_acquired) {
                // 处理GIF更新
                if (message.sync_both_screens) {
                    // 同步两个屏幕
                    if (display->emotion_gif1_ && display->emotion_gif2_) {
                        // 暂停、设置、重启 - 减少中间变量
                        lv_gif_pause(display->emotion_gif1_);
                        lv_gif_pause(display->emotion_gif2_);

                        lv_gif_set_src(display->emotion_gif1_, message.gif_resource);
                        lv_gif_set_src(display->emotion_gif2_, message.gif_resource);

                        lv_gif_restart(display->emotion_gif1_);
                        lv_gif_restart(display->emotion_gif2_);

                        lv_obj_invalidate(display->emotion_gif1_);
                        lv_obj_invalidate(display->emotion_gif2_);

                        #if GIF_DEBUG_SYNC_MESSAGES
                        ESP_LOGI(TAG, "✅ Synchronized GIF update completed");
                        #endif
                    }
                } else {
                    // 单屏更新
                    lv_obj_t* target_gif = (message.target_screen == 1) ?
                                          display->emotion_gif1_ : display->emotion_gif2_;
                    if (target_gif) {
                        lv_gif_set_src(target_gif, message.gif_resource);
                        lv_obj_invalidate(target_gif);
                        #if GIF_DEBUG_SYNC_MESSAGES
                        ESP_LOGI(TAG, "Updated GIF on screen %d", message.target_screen);
                        #endif
                    }
                }

                lvgl_port_unlock();
            } else {
                ESP_LOGE(TAG, "LVGL lock failed after retries");
            }

            // 短暂延时
            vTaskDelay(pdMS_TO_TICKS(10));
        } else {
            // 队列接收超时，正常情况
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
}

void EyeGifDisplay::StartGifSyncTask() {
    if (sync_task_handle_ != nullptr) {
        ESP_LOGW(TAG, "GIF sync task already running");
        return;
    }

    // 在指定核心上创建同步任务，与LVGL任务分离
    BaseType_t result = xTaskCreatePinnedToCore(
        GifSyncTask,
        "gif_sync_task",
        GIF_SYNC_TASK_STACK,     // 堆栈大小
        this,                    // 参数
        GIF_SYNC_TASK_PRIORITY,  // 优先级（高于LVGL任务）
        &sync_task_handle_,      // 任务句柄
        GIF_SYNC_TASK_CORE       // 绑定到指定核心
    );

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create GIF sync task");
        sync_task_handle_ = nullptr;
    } else {
        ESP_LOGI(TAG, "GIF sync task created successfully on core %d", GIF_SYNC_TASK_CORE);
    }

#if GIF_FRAME_CACHE_ENABLED
void EyeGifDisplay::InitializeFrameCache() {
    ESP_LOGI(TAG, "Initializing frame cache");

    // 暂时不创建帧缓存，等到实际需要时再创建
    frame_cache1_ = nullptr;
    frame_cache2_ = nullptr;
    current_gif1_ = nullptr;
    current_gif2_ = nullptr;

    ESP_LOGI(TAG, "Frame cache initialization completed");
}

void EyeGifDisplay::DestroyFrameCache() {
    ESP_LOGI(TAG, "Destroying frame cache");

    if (frame_cache1_) {
        gif_frame_cache_destroy(frame_cache1_);
        frame_cache1_ = nullptr;
    }

    if (frame_cache2_) {
        gif_frame_cache_destroy(frame_cache2_);
        frame_cache2_ = nullptr;
    }

    current_gif1_ = nullptr;
    current_gif2_ = nullptr;

    ESP_LOGI(TAG, "Frame cache destroyed");
}

void EyeGifDisplay::UpdateFrameCache(uint8_t screen_id, const lv_img_dsc_t* gif_dsc) {
    if (!gif_dsc) {
        return;
    }

    ESP_LOGI(TAG, "Updating frame cache for screen %d", screen_id);

    if (screen_id == 1) {
        // 检查是否需要更新缓存
        if (current_gif1_ != gif_dsc) {
            // 销毁旧缓存
            if (frame_cache1_) {
                gif_frame_cache_destroy(frame_cache1_);
                frame_cache1_ = nullptr;
            }

            // 创建新缓存
            frame_cache1_ = gif_frame_cache_create(gif_dsc, width_, height_,
                                                  GIF_FRAME_CACHE_SIZE, GIF_FRAME_CACHE_USE_PSRAM);
            if (frame_cache1_) {
                gif_frame_cache_start_decode_task(frame_cache1_);
                gif_frame_cache_preload_frames(frame_cache1_, 0);
                current_gif1_ = gif_dsc;
                ESP_LOGI(TAG, "Frame cache created for screen 1");
            } else {
                ESP_LOGE(TAG, "Failed to create frame cache for screen 1");
            }
        }
    } else if (screen_id == 2) {
        // 检查是否需要更新缓存
        if (current_gif2_ != gif_dsc) {
            // 销毁旧缓存
            if (frame_cache2_) {
                gif_frame_cache_destroy(frame_cache2_);
                frame_cache2_ = nullptr;
            }

            // 创建新缓存
            frame_cache2_ = gif_frame_cache_create(gif_dsc, width_, height_,
                                                  GIF_FRAME_CACHE_SIZE, GIF_FRAME_CACHE_USE_PSRAM);
            if (frame_cache2_) {
                gif_frame_cache_start_decode_task(frame_cache2_);
                gif_frame_cache_preload_frames(frame_cache2_, 0);
                current_gif2_ = gif_dsc;
                ESP_LOGI(TAG, "Frame cache created for screen 2");
            } else {
                ESP_LOGE(TAG, "Failed to create frame cache for screen 2");
            }
        }
    }
}

void EyeGifDisplay::PrintFrameCacheStats() {
    ESP_LOGI(TAG, "=== Frame Cache Statistics ===");

    if (frame_cache1_) {
        ESP_LOGI(TAG, "Screen 1 cache:");
        gif_frame_cache_print_stats(frame_cache1_);
    } else {
        ESP_LOGI(TAG, "Screen 1 cache: Not initialized");
    }

    if (frame_cache2_) {
        ESP_LOGI(TAG, "Screen 2 cache:");
        gif_frame_cache_print_stats(frame_cache2_);
    } else {
        ESP_LOGI(TAG, "Screen 2 cache: Not initialized");
    }
}
#endif
}

void EyeGifDisplay::StopGifSyncTask() {
    if (sync_task_handle_ != nullptr) {
        vTaskDelete(sync_task_handle_);
        sync_task_handle_ = nullptr;
        ESP_LOGI(TAG, "GIF sync task stopped");
    }
}
