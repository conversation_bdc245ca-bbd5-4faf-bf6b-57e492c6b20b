#pragma once

/**
 * @file gif_animatedgif_adapter.h
 * @brief AnimatedGIF框架适配层
 * 
 * 这个适配层将开源的AnimatedGIF框架适配到我们的gif_decoder接口，
 * 保持原始AnimatedGIF代码不变，只提供接口转换。
 */

#include <stdint.h>
#include <esp_err.h>
#include <lvgl.h>

#ifdef __cplusplus
extern "C" {
#endif

// 前向声明
typedef struct gif_animatedgif_context gif_animatedgif_context_t;

/**
 * @brief AnimatedGIF适配器结构
 */
typedef struct gif_animatedgif_adapter {
    const lv_img_dsc_t* gif_source;    // GIF源数据
    uint32_t width;                    // 图像宽度
    uint32_t height;                   // 图像高度
    uint32_t frame_count;              // 总帧数
    uint32_t current_frame;            // 当前帧索引
    gif_animatedgif_context_t* context; // AnimatedGIF上下文
    uint16_t* frame_buffer;            // 当前帧缓冲区
    uint32_t frame_buffer_size;        // 帧缓冲区大小
    uint32_t last_delay_ms;            // 最后一帧的延迟时间
    bool is_initialized;               // 是否已初始化
} gif_animatedgif_adapter_t;

/**
 * @brief 创建AnimatedGIF适配器
 * 
 * @param gif_source GIF源数据
 * @return gif_animatedgif_adapter_t* 适配器指针，失败返回NULL
 */
gif_animatedgif_adapter_t* gif_animatedgif_adapter_create(const lv_img_dsc_t* gif_source);

/**
 * @brief 销毁AnimatedGIF适配器
 * 
 * @param adapter 适配器指针
 */
void gif_animatedgif_adapter_destroy(gif_animatedgif_adapter_t* adapter);

/**
 * @brief 获取GIF信息
 * 
 * @param adapter 适配器指针
 * @param width 输出宽度
 * @param height 输出高度
 * @param frame_count 输出帧数
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_animatedgif_adapter_get_info(gif_animatedgif_adapter_t* adapter, 
                                           uint32_t* width, uint32_t* height, 
                                           uint32_t* frame_count);

/**
 * @brief 解码指定帧到RGB565缓冲区
 * 
 * @param adapter 适配器指针
 * @param frame_index 帧索引
 * @param output_buffer 输出缓冲区 (RGB565格式)
 * @param buffer_size 缓冲区大小 (字节)
 * @param delay_ms 输出帧延迟时间 (毫秒)
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_animatedgif_adapter_decode_frame(gif_animatedgif_adapter_t* adapter, 
                                              uint32_t frame_index,
                                              uint16_t* output_buffer, 
                                              uint32_t buffer_size,
                                              uint32_t* delay_ms);

/**
 * @brief 重置适配器到第一帧
 * 
 * @param adapter 适配器指针
 * @return esp_err_t ESP_OK成功，其他失败
 */
esp_err_t gif_animatedgif_adapter_reset(gif_animatedgif_adapter_t* adapter);

#ifdef __cplusplus
}
#endif
